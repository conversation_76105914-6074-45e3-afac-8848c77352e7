locals {
  # Environment name shortening for resource names
  env_short = var.environment == "production" ? "prod" : var.environment == "staging" ? "stg" : var.environment

  # Full service name with environment prefix
  service_name = "${local.env_short}-${var.service_name}"

  # S3 bucket name for ALB access logs
  alb_logs_bucket = "bt-alb-logs-${local.env_short}"

  # Common tags including environment
  common_tags = merge(var.tags, {
    env = var.environment
  })

  # Domain names - always created
  internal_domain = "${var.service_name}-int.${local.env_short}.braintrust.dev"
  public_domain   = "${var.service_name}.${local.env_short}.braintrust.dev"
}
