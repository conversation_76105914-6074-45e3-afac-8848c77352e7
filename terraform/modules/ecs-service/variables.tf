variable "service_name" {
  description = "Name of the service (without environment prefix)"
  type        = string
}


variable "environment" {
  description = "Environment name (e.g., production, staging, development)"
  type        = string

  validation {
    condition     = !contains(["prod", "stage"], var.environment)
    error_message = "Environment must not use abbreviations like 'prod' or 'stage'. Use full names instead (e.g., 'production', 'staging')."
  }
}


variable "container_image" {
  description = "Container image URI for the service"
  type        = string
}

variable "container_port" {
  description = "Port the container listens on"
  type        = number
  default     = 8080
}

variable "cpu" {
  description = "CPU units for the ECS task (1024 = 1 vCPU)"
  type        = number
  default     = 1024
  validation {
    condition = contains([
      256,  # 0.25 vCPU
      512,  # 0.5 vCPU
      1024, # 1 vCPU
      2048, # 2 vCPU
      4096, # 4 vCPU
      8192, # 8 vCPU
      16384 # 16 vCPU
    ], var.cpu)
    error_message = "Invalid CPU value for AWS Fargate. Allowed values: 256, 512, 1024, 2048, 4096, 8192, 16384. See https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html for valid values."
  }
}

variable "memory" {
  description = "Memory (MB) for the ECS task"
  type        = number
  default     = 2048
  validation {
    condition = (
      (
        var.cpu == 256 && contains([512, 1024, 2048], var.memory)
      ) ||
      (
        var.cpu == 512 && contains([1024, 2048, 3072, 4096], var.memory)
      ) ||
      (
        var.cpu == 1024 && contains([2048, 3072, 4096, 5120, 6144, 7168, 8192], var.memory)
      ) ||
      (
        var.cpu == 2048 && var.memory >= 4096 && var.memory <= 16384 && var.memory % 1024 == 0
      ) ||
      (
        var.cpu == 4096 && var.memory >= 8192 && var.memory <= 30720 && var.memory % 1024 == 0
      ) ||
      (
        var.cpu == 8192 && var.memory >= 16384 && var.memory <= 61440 && var.memory % 4096 == 0
      ) ||
      (
        var.cpu == 16384 && var.memory >= 32768 && var.memory <= 122880 && var.memory % 8192 == 0
      )
    )
    error_message = "Invalid CPU/memory combination for AWS Fargate. See https://docs.aws.amazon.com/AmazonECS/latest/developerguide/AWS_Fargate.html for valid values."
  }
}

variable "ephemeral_storage" {
  description = "Ephemeral storage size (GiB) for the ECS task"
  type        = number
  default     = 21
  validation {
    condition     = var.ephemeral_storage >= 21 && var.ephemeral_storage <= 200
    error_message = "Ephemeral storage size must be between 21 GiB and 200 GiB."
  }
}

variable "root_storage" {
  description = "Root storage size (GiB) for the ECS task"
  type        = number
  default     = 30
  validation {
    condition     = var.root_storage >= 30
    error_message = "Root storage size must be at least 30 GiB"
  }
}

variable "environment_variables" {
  description = "Environment variables for the container"
  type        = map(string)
  default     = {}
}

variable "secrets" {
  description = "Secrets for the container (from AWS Secrets Manager or Parameter Store)"
  type = list(object({
    name      = string
    valueFrom = string
  }))
  default = []
}

variable "desired_count" {
  description = "Desired number of ECS tasks"
  type        = number
  default     = 2
}

variable "min_count" {
  description = "Minimum number of ECS tasks for auto scaling"
  type        = number
  default     = 2
}

variable "max_count" {
  description = "Maximum number of ECS tasks for auto scaling"
  type        = number
  default     = 10
}

variable "cpu_scaling" {
  description = "Enable CPU-based auto scaling"
  type        = bool
  default     = true
}

variable "cpu_scaling_target" {
  description = "Target CPU utilization for auto scaling"
  type        = number
  default     = 75
}

variable "memory_scaling" {
  description = "Enable memory-based auto scaling"
  type        = bool
  default     = true
}

variable "memory_scaling_target" {
  description = "Target memory utilization for auto scaling"
  type        = number
  default     = 75
}

variable "ecs_cluster_name" {
  description = "Name of the existing ECS cluster to deploy the service to"
  type        = string
}

variable "vpc_config" {
  description = "VPC configuration for the service"
  type = object({
    vpc_id          = string
    public_subnets  = list(string)
    private_subnets = list(string)
  })
}

variable "is_public_service" {
  description = "Whether the service should be accessible from the internet (false = internal ALB). If true, service will have a public domain and certificate."
  type        = bool
  default     = false
}

variable "authorized_security_groups" {
  description = "Map of security group names to IDs that can reach the service on its port (e.g., { \"vpn\" = \"sg-12345678\" })"
  type        = map(string)
  default     = {}
}

variable "authorized_cidr_blocks" {
  description = "Map of CIDR block names to CIDR ranges that can reach the service on its port (e.g., { \"office\" = \"********/24\" })"
  type        = map(string)
  default     = {}
}

variable "log_retention_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 30
}

variable "enable_alb_access_logs" {
  description = "Enable ALB access logs"
  type        = bool
  default     = false
}

variable "kms_key_arn" {
  description = "ARN of the KMS key for encryption"
  type        = string
  default     = null
}

variable "sidecar_containers" {
  description = "List of sidecar containers to run alongside the main container. Each container should be a valid ECS container definition object. See https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_definition_parameters.html#container_definitions for the complete list of supported properties."
  type        = list(any)
  default     = []
}

variable "tags" {
  description = "Tags to apply to all resources"
  type        = map(string)
  default     = {}
}
