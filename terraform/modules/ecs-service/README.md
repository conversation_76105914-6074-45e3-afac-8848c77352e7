# Generic Service Module

This Terraform module creates a complete ECS service deployment with optional Application Load Balancer, auto-scaling, and custom domain support.

## Features

- **ECS Fargate Service**: Deploy containerized applications on AWS ECS Fargate
- **Application Load Balancer**: Optional ALB with HTTP/HTTPS support
- **Auto Scaling**: CPU and memory-based auto-scaling policies
- **Custom Domain**: Optional custom domain with SSL certificate
- **Security Groups**: Configurable security groups for service access
- **CloudWatch Logs**: Centralized logging with configurable retention
- **IAM Roles**: Proper IAM roles for ECS execution and task permissions
- **S3 Access Logs**: ALB access logs to existing S3 bucket (`bt-alb-logs-<environment>`)
- **Sidecar Containers**: Support for additional containers running alongside the main application container

## Usage

### Basic Service (Public)

```hcl
module "my_service" {
  source = "./modules/service"

  service_name = "nginx-hello"
  environment  = "production"

  container_image = "nginx:latest"
  container_port  = 80

  cpu    = 512
  memory = 1024

  ecs_cluster_name = "my-cluster"

  vpc_config = {
    vpc_id         = "vpc-12345678"
    public_subnets = ["subnet-12345678", "subnet-87654321"]
    private_subnets = ["subnet-abcdef12", "subnet-fedcba21"]
  }

  is_public_service = true
  authorized_security_groups = {
    vpn = "sg-12345678"
    bastion = "sg-87654321"
  } # Allow access from specific security groups
}
```
