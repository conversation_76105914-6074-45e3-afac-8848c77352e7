output "alb_dns_name" {
  description = "DNS name of the Application Load Balancer"
  value       = aws_lb.service.dns_name
}

output "alb_zone_id" {
  description = "Zone ID of the Application Load Balancer"
  value       = aws_lb.service.zone_id
}

output "ecs_service_name" {
  description = "Name of the ECS service"
  value       = aws_ecs_service.service.name
}

output "service_security_group_id" {
  description = "Security group ID for the service"
  value       = aws_security_group.service.id
}

output "alb_security_group_id" {
  description = "Security group ID for the ALB"
  value       = aws_security_group.alb.id
}

output "domain_name" {
  description = "Domain name for the service"
  value       = var.is_public_service ? local.public_domain : local.internal_domain
}



output "alb_arn" {
  description = "ARN of the Application Load Balancer"
  value       = aws_lb.service.arn
}

output "target_group_arn" {
  description = "ARN of the ALB target group"
  value       = aws_lb_target_group.service.arn
}

output "ecs_task_definition_arn" {
  description = "ARN of the ECS task definition"
  value       = aws_ecs_task_definition.service.arn
}

output "ecs_execution_role_arn" {
  description = "ARN of the ECS execution role"
  value       = aws_iam_role.ecs_execution_role.arn
}

output "ecs_task_role_arn" {
  description = "ARN of the ECS task role"
  value       = aws_iam_role.ecs_task_role.arn
}

output "certificate_arn" {
  description = "ARN of the ACM certificate"
  value       = var.is_public_service ? aws_acm_certificate.service[0].arn : null
}

output "service_url" {
  description = "HTTPS URL to access the service"
  value       = var.is_public_service ? "https://${local.public_domain}" : "http://${local.internal_domain}"
}
