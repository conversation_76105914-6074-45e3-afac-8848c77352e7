

resource "aws_lb" "service" {
  name               = "${local.service_name}-alb"
  internal           = !var.is_public_service
  load_balancer_type = "application"
  security_groups    = [aws_security_group.alb.id]
  subnets            = var.is_public_service ? var.vpc_config.public_subnets : var.vpc_config.private_subnets

  enable_deletion_protection = false

  dynamic "access_logs" {
    for_each = var.enable_alb_access_logs ? [1] : []
    content {
      bucket  = data.aws_s3_bucket.alb_logs[0].bucket
      prefix  = "${local.env_short}/${var.service_name}"
      enabled = true
    }
  }

  tags = local.common_tags
}

resource "aws_lb_target_group" "service" {
  name     = "${local.service_name}-tg"
  port     = var.container_port
  protocol = "HTTP"
  vpc_id   = var.vpc_config.vpc_id

  target_type = "ip"

  health_check {
    enabled             = true
    healthy_threshold   = 2
    interval            = 10
    matcher             = "200"
    path                = "/"
    port                = "traffic-port"
    protocol            = "HTTP"
    timeout             = 3
    unhealthy_threshold = 2
  }

  tags = local.common_tags
}

resource "aws_acm_certificate" "service" {
  count             = var.is_public_service ? 1 : 0
  domain_name       = local.public_domain
  validation_method = "DNS"

  lifecycle {
    create_before_destroy = true
  }

  tags = local.common_tags
}

resource "aws_route53_record" "service_cert_validation" {
  for_each = var.is_public_service ? {
    for dvo in aws_acm_certificate.service[0].domain_validation_options : dvo.domain_name => {
      name   = dvo.resource_record_name
      record = dvo.resource_record_value
      type   = dvo.resource_record_type
    }
  } : {}

  allow_overwrite = true
  name            = each.value.name
  records         = [each.value.record]
  ttl             = 60
  type            = each.value.type
  zone_id         = data.aws_route53_zone.main.zone_id
}

resource "aws_acm_certificate_validation" "service" {
  count                   = var.is_public_service ? 1 : 0
  certificate_arn         = aws_acm_certificate.service[0].arn
  validation_record_fqdns = [for record in aws_route53_record.service_cert_validation : record.fqdn]
}

resource "aws_lb_listener" "service_https" {
  count             = var.is_public_service ? 1 : 0
  load_balancer_arn = aws_lb.service.arn
  port              = "443"
  protocol          = "HTTPS"
  ssl_policy        = "ELBSecurityPolicy-TLS13-1-3-2021-06"
  certificate_arn   = aws_acm_certificate_validation.service[0].certificate_arn

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.service.arn
  }
}

resource "aws_lb_listener" "service_http" {
  count             = var.is_public_service ? 0 : 1
  load_balancer_arn = aws_lb.service.arn
  port              = "80"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.service.arn
  }
}
