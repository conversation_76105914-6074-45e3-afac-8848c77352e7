import {
  createContext,
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useDeferredValue,
  useEffect,
  useMemo,
  useState,
} from "react";
import { throttle } from "throttle-debounce";
import { spanTypeAttributeValues } from "@braintrust/core";
import { fetchBtql, useFetchBtqlOptions } from "#/utils/btql/btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { type AliasExpr, type Expr } from "@braintrust/btql/parser";
import { z } from "zod";
import { getSpanDisplayConfig } from "./span-display";
import { type SpanData, type Span } from "@braintrust/local";
import { type SpanIdsMap, type PreviewSpan } from "./graph";
import { unknownToString } from "./search-utils";

export type SearchResultField = {
  spanRowId: string;
  comparisonSpanRowId?: string;
  field: string;
};

export type TraceSearchResult = {
  id: string;
  spanId: string;
  comparisonSpanId?: string;
  data: SearchResult;
  type: string;
  matches: {
    field: string;
    value: string;
  }[];
};

type SearchQueryProps = {
  isPending: boolean;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  fetchNextPage: () => void;
};

const TraceSearchSettersContext = createContext<{
  setSearchQuery: Dispatch<SetStateAction<string>>;
  setSearchOpen: Dispatch<SetStateAction<boolean>>;
  setResultIndex: Dispatch<SetStateAction<number | null>>;
  setSearchResults: Dispatch<SetStateAction<TraceSearchResult[] | undefined>>;
  setSearchQueryProps: Dispatch<SetStateAction<SearchQueryProps>>;
  setSelectedIndex: Dispatch<SetStateAction<number>>;
  setActiveSpans: Dispatch<
    SetStateAction<{
      primary?: SpanData;
      comparison?: SpanData;
    }>
  >;
  setSelectedSpanIds: Dispatch<SetStateAction<Set<string>>>;
  setSpanFieldsToSearch: Dispatch<SetStateAction<Record<string, boolean>>>;
  setSpanTypesToSearch: Dispatch<SetStateAction<Record<string, boolean>>>;
}>({
  setSearchQuery: () => {},
  setSearchOpen: () => {},
  setResultIndex: () => {},
  setSearchResults: () => {},
  setSearchQueryProps: () => {},
  setSelectedIndex: () => {},
  setActiveSpans: () => {},
  setSelectedSpanIds: () => {},
  setSpanFieldsToSearch: () => {},
  setSpanTypesToSearch: () => {},
});

const TraceSearchGettersContext = createContext<{
  searchQuery: string;
  isSearchOpen: boolean;
  searchResultFields: SearchResultField[];
  /** this is the selected span in the search result box */
  resultIndex: number | null;
  searchResults: TraceSearchResult[] | undefined;
  searchQueryProps: {
    isPending: boolean;
    isLoading: boolean;
    isFetchingNextPage: boolean;
    hasNextPage: boolean;
    fetchNextPage: () => void;
  };
  selectedIndex: number;
  /**
   * this is used to preserve the selected span at the top of the list
   * when a new search query is entered
   */
  activeSpans: {
    primary?: SpanData;
    comparison?: SpanData;
  };
  selectedSpanIds: Set<string>;
  spanFieldsToSearch: Record<string, boolean>;
  spanTypesToSearch: Record<string, boolean>;
}>({
  searchQuery: "",
  isSearchOpen: false,
  searchResultFields: [],
  resultIndex: null,
  searchQueryProps: {
    isPending: true,
    isLoading: false,
    isFetchingNextPage: false,
    hasNextPage: false,
    fetchNextPage: () => {},
  },
  searchResults: [],
  selectedIndex: 0,
  activeSpans: {},
  selectedSpanIds: new Set(),
  spanFieldsToSearch: {},
  spanTypesToSearch: {},
});

export const TraceSearchProvider = ({
  activeRowId,
  children,
}: {
  activeRowId: string | null;
  children: React.ReactNode;
}) => {
  const [spanFieldsToSearch, setSpanFieldsToSearch] = useState<
    Record<string, boolean>
  >(
    Object.fromEntries(
      Object.keys(searchResultQueryDataSchema.shape).map((key) => [key, true]),
    ),
  );

  const [spanTypesToSearch, setSpanTypesToSearch] = useState<
    Record<string, boolean>
  >(Object.fromEntries(searchSpanTypes.map((type) => [type, true])));
  // this is the text that's shown in the UI
  const [searchText, setSearchText] = useState("");
  // this is a throttled value for querying
  const [searchQueryRaw, _setSearchQuery] = useState("");
  const throttledSetSearchQuery = useMemo(() => {
    return throttle(200, _setSearchQuery);
  }, [_setSearchQuery]);

  const setSearchQuery = useCallback(
    (v: SetStateAction<string>) => {
      setSearchText(v);
      throttledSetSearchQuery(v);
    },
    [setSearchText, throttledSetSearchQuery],
  );
  const searchQuery = useDeferredValue(searchQueryRaw);
  const [isSearchOpen, setSearchOpen] = useState(false);
  const [activeSpans, setActiveSpans] = useState({});
  // this is the selected span in the search result box
  const [resultIndex, setResultIndex] = useState<number | null>(null);
  const [searchResultsRaw, setSearchResults] = useState<
    TraceSearchResult[] | undefined
  >(undefined);
  const searchResults = useDeferredValue(searchResultsRaw);
  const searchResultFields = useMemo(
    () =>
      (searchResults ?? []).flatMap(({ id, comparisonSpanId, matches }) =>
        (matches ?? []).map(({ field }) => ({
          spanRowId: id,
          comparisonSpanRowId: comparisonSpanId,
          field,
        })),
      ),
    [searchResults],
  );
  const [searchQueryProps, setSearchQueryProps] = useState<SearchQueryProps>({
    isPending: true,
    isLoading: false,
    isFetchingNextPage: false,
    hasNextPage: false,
    fetchNextPage: () => {},
  });

  // This is the keyboard interaction index
  const [selectedIndex, setSelectedIndex] = useState<number>(0);
  const [selectedSpanIds, setSelectedSpanIds] = useState<Set<string>>(
    new Set(),
  );

  useEffect(() => {
    setSearchQuery("");
    setSearchOpen(false);
    setResultIndex(null);
    setSearchResults([]);
    setSelectedIndex(0);
  }, [activeRowId, setSearchQuery]);

  useEffect(() => {
    if (!isSearchOpen) {
      setResultIndex(null);
      setSearchResults([]);
      setSelectedIndex(0);
      setSearchQuery("");
    }
  }, [isSearchOpen, setSearchQuery]);

  return (
    <TraceSearchSettersContext.Provider
      value={useMemo(
        () => ({
          setSearchQuery,
          setSearchOpen,
          setResultIndex,
          setSearchResults,
          setSearchQueryProps,
          setSelectedIndex,
          setActiveSpans,
          setSelectedSpanIds,
          setSpanFieldsToSearch,
          setSpanTypesToSearch,
        }),
        [
          setSearchQuery,
          setSearchOpen,
          setResultIndex,
          setSearchResults,
          setSearchQueryProps,
          setSelectedIndex,
          setActiveSpans,
          setSelectedSpanIds,
          setSpanFieldsToSearch,
          setSpanTypesToSearch,
        ],
      )}
    >
      <TraceSearchGettersContext.Provider
        value={useMemo(
          () => ({
            searchText,
            searchQuery,
            isSearchOpen,
            searchResultFields,
            resultIndex,
            searchResults,
            searchQueryProps,
            selectedIndex,
            activeSpans,
            selectedSpanIds,
            spanFieldsToSearch,
            spanTypesToSearch,
          }),
          [
            isSearchOpen,
            resultIndex,
            searchText,
            searchQuery,
            searchResultFields,
            searchResults,
            searchQueryProps,
            selectedIndex,
            activeSpans,
            selectedSpanIds,
            spanFieldsToSearch,
            spanTypesToSearch,
          ],
        )}
      >
        {children}
      </TraceSearchGettersContext.Provider>
    </TraceSearchSettersContext.Provider>
  );
};

export const useTraceSearchGetters = () => {
  return useContext(TraceSearchGettersContext);
};

export const useTraceSearchSetters = () => {
  return useContext(TraceSearchSettersContext);
};

export const useTraceSearch = () => {
  return {
    ...useTraceSearchGetters(),
    ...useTraceSearchSetters(),
  };
};

const searchSpanTypes = [...spanTypeAttributeValues, "other"];

const searchResultQueryDataSchema = z.object({
  name: z.string().nullish(),
  scores: z.record(z.string(), z.number().nullable()).nullish(),
  input: z.any().nullish(),
  output: z.any().nullish(),
  expected: z.any().nullish(),
  metadata: z.any().nullish(),
  tags: z.array(z.string()).nullish(),
});

const spanAttributesSchema = z.object({
  name: z.string(),
  type: z.string().nullish(),
});

const searchResultSchema = z
  .object({
    id: z.string(),
    span_id: z.string(),
    span_attributes: spanAttributesSchema.nullish(),
  })
  .extend(searchResultQueryDataSchema.shape);

type SearchResult = z.infer<typeof searchResultSchema> &
  Record<string, unknown>;

const rowSchema = z.object({
  id: z.string(),
  span_id: z.string(),
});

type SearchParams = {
  objectType: DataObjectType;
  objectId: string;
  rootSpanId: string;
};

export function useTraceSearchQuery({
  primarySearchParams,
  comparisonSearchParams,
  spanIdsMap,
}: {
  primarySearchParams?: SearchParams;
  comparisonSearchParams?: SearchParams;
  spanIdsMap?: SpanIdsMap;
}) {
  const {
    isSearchOpen,
    searchQuery,
    spanFieldsToSearch,
    spanTypesToSearch,
    setSearchResults,
    setSearchQueryProps,
    activeSpans,
  } = useTraceSearch();
  const queryClient = useQueryClient();
  useEffect(() => {
    if (!isSearchOpen || !searchQuery) {
      queryClient.removeQueries({
        queryKey: ["traceSearch"],
      });
    }
  }, [isSearchOpen, searchQuery, queryClient]);

  useEffect(() => {
    return () => {
      queryClient.resetQueries({ queryKey: ["traceSearch"] });
    };
  }, [queryClient]);

  const { result: primaryQueryResult, enabled: primaryQueryEnabled } =
    usePaginatedSearchQuery({
      searchParams: primarySearchParams,
      spanFieldsToSearch,
      spanTypesToSearch,
      enabled: isSearchOpen && !!searchQuery,
    });

  const { result: comparisonQueryResult, enabled: comparisonQueryEnabled } =
    usePaginatedSearchQuery({
      searchParams: comparisonSearchParams,
      spanFieldsToSearch,
      spanTypesToSearch,
      enabled: isSearchOpen && !!searchQuery,
    });

  const enabled = primaryQueryEnabled || comparisonQueryEnabled;
  const searchData = useMemo(() => {
    if (!enabled) {
      return [];
    }
    return [
      ...(primaryQueryResult.data?.pages.flatMap((p) => p.data) ?? []),
      ...(comparisonQueryResult.data?.pages.flatMap((p) => p.data) ?? []),
    ].flatMap(prepareSearchData);
  }, [enabled, primaryQueryResult.data, comparisonQueryResult.data]);

  const searchDataWithActiveSpans = useMemo(() => {
    if (!enabled) {
      return [];
    }
    // put active span at search time to the top of the list
    return [
      ...(activeSpans.primary ? prepareSearchData(activeSpans.primary) : []),
      ...(activeSpans.comparison
        ? prepareSearchData(activeSpans.comparison)
        : []),
      ...searchData.filter((d) => {
        if (
          (activeSpans.primary && activeSpans.primary.id === d.data.id) ||
          (activeSpans.comparison && activeSpans.comparison.id === d.data.id)
        ) {
          return false;
        }
        return true;
      }),
    ];
  }, [enabled, searchData, activeSpans.primary, activeSpans.comparison]);

  const searchResults = useMemo(() => {
    const fields = Object.keys(spanFieldsToSearch).filter(
      (f) => spanFieldsToSearch[f],
    );

    const searchQueryLower = searchQuery.toLowerCase();
    return searchDataWithActiveSpans.flatMap(({ data, type, stringValues }) => {
      if (!spanTypesToSearch[data.span_attributes?.type ?? "other"]) {
        return [];
      }
      const matches = fields.flatMap((f) => {
        const value = stringValues[f];
        if (value.indexOf(searchQueryLower) === -1) {
          return [];
        }
        return [{ field: f, value }];
      });

      if (matches.length === 0) {
        return [];
      }
      return [
        {
          id: data.id,
          spanId: data.span_id,
          data,
          type,
          matches,
        },
      ];
    });
  }, [
    searchDataWithActiveSpans,
    spanFieldsToSearch,
    spanTypesToSearch,
    searchQuery,
  ]);

  const searchResultsWithComparisonIds = useMemo(() => {
    return searchResults.map((result) => {
      return {
        ...result,
        matches: result.matches.map((match) => {
          const comparisonSpanId = spanIdsMap?.[result.data.span_id];
          return {
            ...match,
            comparisonSpanId: comparisonSpanId?.id,
          };
        }),
      };
    });
  }, [searchResults, spanIdsMap]);

  useEffect(() => {
    setSearchResults(enabled ? searchResultsWithComparisonIds : undefined);
  }, [enabled, searchResultsWithComparisonIds, setSearchResults]);

  const fetchNextPrimary = primaryQueryResult.fetchNextPage;
  const fetchNextComparison = comparisonQueryResult.fetchNextPage;
  const fetchNextPage = useCallback(() => {
    if (!searchQuery) {
      return;
    }
    // https://stackoverflow.com/a/72893761
    if (primaryQueryEnabled) {
      fetchNextPrimary({ cancelRefetch: false });
    }
    if (comparisonQueryEnabled) {
      fetchNextComparison({ cancelRefetch: false });
    }
  }, [
    searchQuery,
    primaryQueryEnabled,
    comparisonQueryEnabled,
    fetchNextPrimary,
    fetchNextComparison,
  ]);

  const isPending =
    primaryQueryResult.isPending || comparisonQueryResult.isPending;
  const isLoading =
    primaryQueryResult.isLoading || comparisonQueryResult.isLoading;
  const isFetchingNextPage =
    primaryQueryResult.isFetchingNextPage ||
    comparisonQueryResult.isFetchingNextPage;
  const hasNextPage =
    primaryQueryResult.hasNextPage || comparisonQueryResult.hasNextPage;

  const searchQueryProps = useMemo(() => {
    return {
      isPending,
      isLoading,
      isFetchingNextPage,
      hasNextPage,
      fetchNextPage,
    };
  }, [isPending, isLoading, hasNextPage, isFetchingNextPage, fetchNextPage]);

  useEffect(() => {
    setSearchQueryProps(searchQueryProps);
  }, [searchQueryProps, setSearchQueryProps]);
}

function parseSpanAttributes(spanAttributesValue: unknown) {
  try {
    const spanAttributes = spanAttributesSchema.parse(
      typeof spanAttributesValue === "string"
        ? JSON.parse(spanAttributesValue)
        : spanAttributesValue,
    );
    return spanAttributes;
  } catch {
    return undefined;
  }
}

function prepareSearchData(data: SearchResult | null | undefined) {
  if (!data) {
    return [];
  }
  const spanAttributes = parseSpanAttributes(data.span_attributes);
  return [
    {
      data,
      type: spanAttributes?.type ?? "other",
      stringValues: Object.fromEntries(
        Object.keys(searchResultSchema.shape).map((k) => {
          const value =
            k !== "name" || "name" in data ? data[k] : spanAttributes?.name;
          const stringValue =
            k === "tags" && Array.isArray(value)
              ? (value.join(", ") ?? "")
              : unknownToString(value);
          return [k, stringValue.toLowerCase()];
        }),
      ),
    },
  ];
}

const PAGE_SIZE = 5;

function usePaginatedSearchQuery({
  searchParams,
  spanFieldsToSearch,
  spanTypesToSearch,
  enabled,
}: {
  searchParams?: SearchParams;
  spanFieldsToSearch: Record<string, boolean>;
  spanTypesToSearch: Record<string, boolean>;
  enabled?: boolean;
}) {
  const btqlOptions = useFetchBtqlOptions();
  const builder = useBtqlQueryBuilder({});
  const fields =
    searchParams?.objectType === "dataset"
      ? ["input", "expected", "metadata"]
      : Object.keys(spanFieldsToSearch).filter((t) => spanFieldsToSearch[t]);
  const spanTypes = Object.keys(spanTypesToSearch).filter(
    (t) => spanTypesToSearch[t],
  );
  const queryEnabled = !!searchParams && !!enabled && spanTypes.length > 0;
  const result = useInfiniteQuery({
    queryKey: [
      "traceSearch",
      searchParams?.objectType,
      searchParams?.objectId,
      searchParams?.rootSpanId,
      fields,
      spanTypes,
    ],
    queryFn: async ({ signal, pageParam: cursor }) => {
      const rootSpanFilter: Expr = {
        op: "eq",
        left: { btql: "root_span_id" },
        right: {
          op: "literal",
          value: searchParams!.rootSpanId,
        },
      };
      const res = await fetchBtql({
        args: {
          query: {
            from: builder.from(
              searchParams!.objectType,
              searchParams!.objectId ? [searchParams!.objectId] : [],
              "spans",
            ),
            select: ["id", "span_id", ...fields].map((_f): AliasExpr => {
              const f = _f === "name" ? "span_attributes" : _f;
              return {
                alias: f,
                expr: builder.ident(f),
              };
            }),
            filter:
              searchParams?.objectType === "dataset"
                ? rootSpanFilter
                : builder.and(
                    rootSpanFilter,
                    builder.or(
                      ...spanTypes.map(
                        (t): Expr =>
                          t === "other"
                            ? {
                                op: "isnull",
                                expr: builder.ident("span_attributes", "type"),
                              }
                            : {
                                op: "eq",
                                left: builder.ident("span_attributes", "type"),
                                right: {
                                  op: "literal",
                                  value: t,
                                },
                              },
                      ),
                    ),
                  ),
            limit: PAGE_SIZE,
            ...(cursor ? { cursor } : {}),
          },
          brainstoreRealtime: true,
          useColumnstore: false,
        },
        ...btqlOptions,
        signal,
        schema: rowSchema,
      });
      return res;
    },
    getNextPageParam: (lastPage) => {
      return lastPage.data.length > 0 ? lastPage.cursor : undefined;
    },
    initialPageParam: "",
    enabled: queryEnabled,
    staleTime: Infinity,
  });

  return { result, enabled: queryEnabled };
}

export type UseTraceSearchQueryProps = ReturnType<typeof useTraceSearchQuery>;

export function flattenSearchResults<T>(
  searchResults: TraceSearchResult[] | undefined,
  spans?: Record<string, (Span | PreviewSpan) & T>,
  comparisonSpans?: Record<string, (Span | PreviewSpan) & T>,
) {
  return (searchResults ?? []).flatMap((result) => {
    const comparisonSpan = comparisonSpans?.[result.data.span_id];
    const span = spans?.[result.data.span_id] ?? comparisonSpan;
    if (!span) return [];
    const spanConfig =
      span &&
      getSpanDisplayConfig({
        type: span.data.span_attributes.type,
        cached: false,
        remote: false,
        hasError: false,
      });
    return (result.matches ?? []).flatMap(({ field, value }) => {
      const fullSpan: Span & T = {
        ...span,
        data: {
          ...span.data,
          ...result.data,
        },
      };
      return !field || !value
        ? []
        : [
            {
              spanId: span.id,
              field,
              value,
              span: fullSpan,
              type: span.data.span_attributes.type,
              spanConfig,
              isComparison: !!comparisonSpan,
            },
          ];
    });
  });
}
