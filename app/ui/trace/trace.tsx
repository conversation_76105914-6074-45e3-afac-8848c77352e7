"use client";

import { prepareRowsForCopy } from "#/app/app/[org]/p/[project]/experiments/[experiment]/ExperimentSelectionSection";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import type { DataObjectType } from "#/utils/btapi/btapi";
import { cn } from "#/utils/classnames";
import { type UpdateLog } from "#/utils/duckdb";
import {
  type UpdateRowFn,
  type CommentFn,
  type DeleteCommentFn,
} from "#/utils/mutable-object";
import { isEmpty, isObject } from "#/utils/object";
import { useOtherObjectInserter } from "#/utils/other-object-inserter";
import { useOrg, useUser } from "#/utils/user";
import { TRANSACTION_ID_FIELD } from "@braintrust/core";
import {
  type TransitionStartFunction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type BatchUpdateRowFn } from "#/ui/arrow-table";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { type SavingState } from "#/ui/saving";
import { ActionButton } from "#/ui/trace/ActionButton";
import {
  type LoadedTrace,
  type Span,
  type Trace,
  diffTraces,
  getSpanBySpanId,
  makeDiffSpan,
} from "#/ui/trace/graph";
import {
  diffKeynameForIndex,
  DiffLeftField,
  type DiffObjectType,
  DiffRightField,
  flattenDiffObjects,
  isDiffObject,
} from "#/utils/diffs/diff-objects";
import { useRowAuditLog } from "#/ui/trace/query";
import { useTraceShortcuts } from "#/ui/trace/shortcuts";
import {
  ArrowLeftToLine,
  FoldVertical,
  Hourglass,
  Minimize2,
  SearchSlash,
  Timer,
  TimerOff,
  TriangleAlert,
  UnfoldVertical,
  XIcon,
} from "lucide-react";
import { Tags } from "#/ui/trace/tags";
import { ExpandedHumanReviewModal } from "#/ui/trace/expanded-human-review-modal";
import { TraceTree } from "#/ui/trace/trace-tree";
import { type DataDisplayedField } from "#/ui/trace/data-display";
import { ScrollableContainerWithOptions } from "#/ui/trace/scrollable-container-with-options";
import {
  useHumanReviewState,
  useAttachmentBrowser,
  useTraceViewTypeState,
  useActiveRowAndSpan,
} from "#/ui/query-parameters";
import { type Roster } from "#/utils/realtime-data";
import { RosterAvatars } from "#/ui/roster";
import { useEntityStorage } from "#/lib/clientDataStorage";
import { ConfirmationDialog } from "#/ui/dialogs/confirmation";
import { HotkeyScope } from "#/ui/hotkeys";
import { useHotkeysContext } from "react-hotkeys-hook";
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from "#/ui/resizable";
import { usePanelSize } from "#/ui/use-panel-size";
import { Spinner } from "#/ui/icons/spinner";
import { PaginatedTraceSearch } from "./trace-search";
import { TraceHeader } from "./trace-header";
import { useTraceCopilotContext } from "#/ui/copilot/trace";
import { type ApplySearch } from "#/ui/use-filter-sort-search";
import { Skeleton } from "#/ui/skeleton";
import { SpanContents } from "./span-contents";

import { GoToOriginProvider } from "./go-to-origin-context";
import { TraceSearchResults } from "./trace-search-results";
import { type Dataset } from "@braintrust/core/typespecs";
import { Tag } from "#/ui/tag";
import { SpanHeader, SpanName } from "./span-header";
import {
  RowComparisonSelection,
  isDiffModeRowId,
} from "./RowComparisonSelection";
import { freeFormDataPath } from "./free-form-text-area";
import { Dialog, DialogContent, DialogTitle } from "#/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { EXPERIMENT_COMPARISON_COLOR_CLASSNAMES } from "#/ui/charts/colors";
import { useComparisonRowData } from "#/ui/row-comparison/useComparisonRowData";
import { type ComparisonExperimentSpanSummary } from "#/app/app/[org]/p/[project]/experiments/[experiment]/(queries)/useExperiment";
import { IFrameViewer, RefreshIframeButton } from "#/ui/iframe-viewer";
import { useExpandedSpanIframe } from "./use-expanded-span-iframe";
import { Button } from "#/ui/button";
import { TableEmptyState } from "#/ui/table/TableEmptyState";
import useEvent from "react-use-event-hook";
import { AttachmentBrowser } from "#/ui/attachment/attachment-browser";
import { type CustomColumnsRowParams } from "#/utils/custom-columns/use-expanded-row-custom-columns";
import { getExpandedRowParamsPlayground } from "./playground";
import { RESERVED_TAGS } from "#/app/app/[org]/p/[project]/configuration/tags/tag-dialog";
import { useExtractScores } from "./use-extract-scores";
import useSpanSelection from "./use-span-selection";
import { SpanField } from "./use-span-field-order.tsx";
import { useTraceSearch, useTraceSearchQuery } from "./trace-search-context";
import { type PlaygroundRecord } from "#/app/app/[org]/p/[project]/playgrounds/[playground]/playx/playx";
import { type Virtualizer } from "@tanstack/react-virtual";
import { ThreadSpanMetrics } from "./thread-span-metrics";
import { TraceLayoutDropdown } from "./trace-layout-dropdown";
import { useVirtualTrace } from "./use-virtual-trace";
import { type ModelCosts } from "#/ui/prompts/models";
import { useQueryClient } from "@tanstack/react-query";
import { useLoadFullSpans } from "./use-load-full-spans";
import { useTraceFullscreen } from "./use-trace-fullscreen";
import { type RealtimeState } from "@braintrust/local/app-schema";

export interface TraceProps {
  rowId: string | DiffObjectType<string>;
  rowIndex?: number;
  totalRows?: number;
  traceViewParams: TraceViewParams;
  onClose: VoidFunction;
  onPrevRow?: (opts?: { withTransition?: boolean }) => Promise<unknown>;
  onNextRow?: (opts?: { withTransition?: boolean }) => Promise<unknown>;
  onJumpToRow?: (rowIndex: number) => void;
  onApplySearch: ApplySearch;
  isReadOnly?: boolean;
  isDelayedSpanChangeTransitioning: boolean;
  startSpanChangeTransition: TransitionStartFunction;
}
export interface ExpandedRowParams {
  primaryScan: string | null;
  primaryScanReady: number[];
  primaryScanChannel: UpdateLog | null;
  primaryDynamicObjectId: string | null;
  comparisonDynamicObjectId?: string | null;
  comparisonDynamicObjectType?: DataObjectType | null;
  auditLogScan: string | null;
  auditLogScanReady: number[];
  modelSpecScan?: string | null;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  multiExperimentData?: {
    comparisonExperimentData: ComparisonExperimentSpanSummary[];
  };
  hasTrials?: boolean;
  customColumnsParams: CustomColumnsRowParams;
  isFastSummaryEnabled?: boolean;
}

export interface TraceViewParams {
  title: string;
  objectType: DataObjectType;
  objectName: string;
  objectId: string | undefined;
  expandedRowParams: ExpandedRowParams;
  roster: Roster;
  experimentNames?: { current: string; comparison?: string };
  selectableExperimentsList?: {
    id: string;
    name: string;
    created?: string | null;
    scan?: string | null;
    refreshed?: number;
  }[];
  editableFields: DataDisplayedField[];
  hiddenFields?: DataDisplayedField[];
  updateRow?: UpdateRowFn;
  batchUpdateRow?: BatchUpdateRowFn;
  commentFn?: CommentFn;
  deleteCommentFn?: DeleteCommentFn;
  savingState?: SavingState;
  playXProps?: {
    runPrompts: (args: {
      rowIdx?: number; // playx uses the id rather than the ordinal, so we can remove this once we've migrated
      datasetRecordId?: string;
      datasetXactId?: string;
      inputValue?: Partial<PlaygroundRecord>;
      updateRowIds?: string[];
    }) => void;
    stop: (rowIdx: number | null) => void;
    isRunning: boolean;
    datasetDml: {
      updateRow: UpdateRowFn;
      commentFn: CommentFn;
      deleteCommentFn: DeleteCommentFn;
    };
    datasetEditableFields: DataDisplayedField[];
  };
}

const HUMAN_REVIEW_DEFAULT_COLLAPSED_FIELDS = [SpanField.SCORES];

export function TraceViewer({
  traceViewParams,
  rowId,
  rowIndex,
  totalRows,
  onClose,
  onPrevRow,
  onNextRow,
  onJumpToRow,
  onApplySearch,
  isReadOnly,
  isDelayedSpanChangeTransitioning,
  startSpanChangeTransition,
}: TraceProps) {
  const { user } = useUser();
  const [viewType, setViewType] = useTraceViewTypeState();

  const {
    objectType: objectTypeProp,
    objectName,
    objectId,
    expandedRowParams,
    experimentNames,
    batchUpdateRow,
    updateRow: updateRowProp,
    editableFields: editableFieldsProp,
    hiddenFields,
    selectableExperimentsList,
    roster,
    playXProps,
  } = traceViewParams;

  const { id: orgId, name: orgName } = useOrg();

  const {
    mutateDatasets,
    projectId,
    projectName,
    config: projectConfig,
  } = useContext(ProjectContext);

  const addRowsToDataset = useOtherObjectInserter({
    objectType: "dataset",
  });

  const performAddRowsToDataset = useCallback(
    ({
      datasetId,
      datasetName,
      spans,
      selectedProjectId,
      selectedProjectName,
    }: {
      datasetId: string;
      datasetName: string;
      spans: Span[] | null;
      selectedProjectId?: string;
      selectedProjectName?: string;
    }) => {
      if (!projectId || spans === null || spans.length === 0) {
        return;
      }
      const rows = spans.map((span) => {
        // TODO(kevin): Remove hack.
        // Why does it have SpanData type but can be diff?
        const row = { ...span.data };
        for (const key of Object.keys(row)) {
          if (isDiffObject(row[key])) {
            row[key] = row[key][DiffLeftField];
          }
        }
        return row;
      });
      addRowsToDataset(
        prepareRowsForCopy({
          orgName,
          projectName: selectedProjectName ?? projectName,
          projectId: selectedProjectId ?? projectId,
          objectType: objectTypeProp,
          objectId,
          selectedRowsWithData: rows,
          targetDataset: {
            id: datasetId,
            name: datasetName,
          },
          expectedFieldSource: "auto",
          cleanup: () => {},
        }),
      );
    },
    [
      addRowsToDataset,
      orgName,
      projectId,
      projectName,
      objectId,
      objectTypeProp,
    ],
  );

  const { modal: createDatasetModal, open: openCreateDatasetDialog } =
    useCreateDatasetDialog({
      onSuccessfulCreate: ({ datasetId, datasetName, projectName }) => {
        performAddRowsToDataset({
          datasetName: datasetName,
          datasetId: datasetId,
          spans: selectedSpan ? [selectedSpan] : null,
          selectedProjectName: projectName,
        });
        mutateDatasets?.();
      },
      orgId,
      projectName,
    });

  const primaryRowId =
    (isDiffObject(rowId) ? rowId[DiffRightField] : rowId) || null;

  const dynamicRowType = getExpandedRowParamsPlayground(rowId);
  const objectType = dynamicRowType?.objectType ?? objectTypeProp;
  const dynamicObjectId =
    dynamicRowType?.objectId ?? expandedRowParams.primaryDynamicObjectId;
  const editableFields =
    objectType === "dataset" && playXProps
      ? playXProps.datasetEditableFields
      : editableFieldsProp;
  const updateRow =
    objectType === "dataset"
      ? (playXProps?.datasetDml.updateRow ?? updateRowProp)
      : updateRowProp;

  const {
    trace: primaryTrace,
    comparisonKey,
    isPending: isPrimaryTracePending,
    realtimeState: traceRealtimeState,
  } = useVirtualTrace({
    rowId: primaryRowId,
    objectType,
    objectId: dynamicObjectId,
    isFastSummaryEnabled: expandedRowParams.isFastSummaryEnabled,
  });
  const copilotContext = useTraceCopilotContext({
    objectType,
    objectName,
  });

  const [{ s: activeSpanId }, setActiveRowAndSpan] = useActiveRowAndSpan();
  const setActiveSpan = useCallback(
    (spanRowId: string | null) => {
      if (!spanRowId) {
        return;
      }
      setActiveRowAndSpan({ s: spanRowId });
    },
    [setActiveRowAndSpan],
  );
  const primarySpanRowId =
    primaryTrace?.spans[activeSpanId ?? ""]?.id ?? primaryRowId;
  const {
    traceWithLoadedSpans: loadedTrace,
    expandedRowState,
    loadedSpan: primaryLoadedSpan,
    isPending: isPrimaryRowPending,
    realtimeState: spanRealtimeState,
  } = useLoadFullSpans({
    trace: primaryTrace,
    spanRowId: primarySpanRowId,
    isRoot:
      primarySpanRowId === primaryRowId ||
      (!!primaryTrace?.root.id && primaryTrace.root.id === primarySpanRowId),
    objectType,
    objectId: dynamicObjectId ?? "",
    customColumns: expandedRowParams.customColumnsParams?.customColumns,
  });

  const {
    queryEnabled: isComparisonRowQueryEnabled,
    isPending: isComparisonRowsPending,
    comparisonRowId: selectedCompareRowId,
    comparisonRowIds,
    comparisonExperiment,
    comparisonExperimentIndex,
    setComparisonExperimentId,
    setSelectedComparisonParams,
  } = useComparisonRowData({
    sourceData: {
      type: "query",
      enabled: !!expandedRowParams.hasTrials && isDiffModeRowId(rowId),
    },
    comparisonKey,
    comparisonExperimentData:
      expandedRowParams.multiExperimentData?.comparisonExperimentData,
  });
  const comparisonRowParams = comparisonExpandedRowParams({
    rowId,
    // Send an empty string when the query is enabled so that we don't fall back to the default comparison row
    // This helps with flickering loading states when switching between rows
    selectedRowId: isComparisonRowQueryEnabled
      ? (selectedCompareRowId ?? "")
      : null,
    expandedRowParams,
    selectedExperimentIndex: comparisonExperimentIndex,
    makeDynamicObjectId:
      expandedRowParams.primaryDynamicObjectId !== null ||
      !!expandedRowParams.comparisonDynamicObjectId,
    comparisonDynamicObjectId: expandedRowParams.comparisonDynamicObjectId,
  });
  const { trace: comparisonTracePreview } = useVirtualTrace({
    ...comparisonRowParams,
    objectType: expandedRowParams.comparisonDynamicObjectType ?? objectType,
    objectId: comparisonRowParams.dynamicObjectId,
    isFastSummaryEnabled: expandedRowParams.isFastSummaryEnabled,
    allowEmpty: true,
  });
  const comparisonRowId = comparisonRowParams.rowId;

  const { trace: traceDiffPreview, spanIdsMap } = useMemo(() => {
    if (primaryTrace && comparisonTracePreview) {
      return diffTraces(comparisonTracePreview, primaryTrace);
    }
    return { trace: primaryTrace, spanIdsMap: {} };
  }, [primaryTrace, comparisonTracePreview]);
  const comparisonSpanPreview = getSpanBySpanId({
    spanId: activeSpanId,
    spanIdsMap,
    spans: comparisonTracePreview?.spans,
  });
  const { loadedSpan: comparisonLoadedSpan } = useLoadFullSpans({
    trace: comparisonTracePreview,
    spanRowId: comparisonSpanPreview?.id ?? null,
    isRoot:
      !!comparisonTracePreview?.root.id &&
      comparisonTracePreview.root.id === comparisonSpanPreview?.id,
    objectType: expandedRowParams.comparisonDynamicObjectType ?? objectType,
    objectId: comparisonRowParams.dynamicObjectId ?? "",
    customColumns: expandedRowParams.customColumnsParams?.customColumns,
  });

  const { setSelectedSpan, scrollTo, scrollToSpanId, setScrollTo } =
    useSpanSelection({
      copilotContext,
      startSpanChangeTransition,
      trace: primaryTrace,
      loadedTrace,
      comparisonTrace: comparisonTracePreview,
      skipUpdateUrl: true,
    });

  const [selectedLoadedTrace, setSelectedLoadedTrace] =
    useState<LoadedTrace | null>(null);
  const trace = selectedLoadedTrace;
  const [selectedLoadedSpan, setSelectedLoadedSpan] = useState<Span | null>(
    null,
  );
  const selectedSpan = selectedLoadedSpan;

  const hasPrimarySpan = !!primarySpanRowId;
  const primarySelectedSpan =
    primaryLoadedSpan?.data.span_id === activeSpanId ||
    ((!activeSpanId ||
      // if the span_id in the url isn't provided or doesn't match the trace
      // then we want to load the root span, which should be seeded from the row id
      !primaryTrace?.spans[activeSpanId ?? ""]) &&
      primaryLoadedSpan?.data.id === primaryRowId)
      ? (primaryLoadedSpan ?? null)
      : null;
  const hasComparisonSpan = !!comparisonSpanPreview?.id;
  const comparisonSelectedSpan =
    comparisonLoadedSpan?.data.id === comparisonSpanPreview?.id
      ? comparisonLoadedSpan
      : null;
  const hasLoadedTrace = !!loadedTrace;
  const activeLoadedTrace =
    loadedTrace?.root?.data.id === primaryRowId ? loadedTrace : null;
  const [selectedPreviewTrace, setSelectedPreviewTrace] =
    useState<Trace | null>(null);
  // handle flickering when switching between:
  // - selected spans
  // - diff mode
  // - diffed traces
  useEffect(() => {
    if (!hasLoadedTrace || !activeLoadedTrace) {
      return;
    }
    let span: Span | null = null;
    if (
      hasPrimarySpan &&
      primarySelectedSpan &&
      hasComparisonSpan &&
      comparisonSelectedSpan
    ) {
      span = makeDiffSpan(primarySelectedSpan, comparisonSelectedSpan);
    } else if (!hasPrimarySpan && hasComparisonSpan && comparisonSelectedSpan) {
      span = makeDiffSpan(undefined, comparisonSelectedSpan);
    } else if (hasPrimarySpan && !hasComparisonSpan) {
      span = primarySelectedSpan;
    }

    if (span) {
      setSelectedLoadedTrace(loadedTrace);
      setSelectedPreviewTrace((prev) => (!prev ? loadedTrace : prev));
      setSelectedLoadedSpan(span);
      setActiveRowAndSpan({
        s: span.data.span_id,
      });
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions, @typescript-eslint/no-explicit-any
      (window as any).currentSpan = span;
    }
  }, [
    hasPrimarySpan,
    hasComparisonSpan,
    primarySelectedSpan,
    comparisonSelectedSpan,
    setSelectedLoadedSpan,
    hasLoadedTrace,
    activeLoadedTrace,
    loadedTrace,
    setActiveRowAndSpan,
  ]);

  const isTransitioningToNewSpan =
    isDelayedSpanChangeTransitioning || !primarySelectedSpan;

  const hasLoadedDiffTrace =
    !!primaryTrace && (!comparisonRowParams.rowId || !!comparisonTracePreview);
  const activeDiffTrace =
    activeLoadedTrace &&
    (comparisonTracePreview?.root.data.id ?? null) === comparisonRowParams.rowId
      ? traceDiffPreview
      : null;
  useEffect(() => {
    if (hasLoadedDiffTrace && activeDiffTrace) {
      setSelectedPreviewTrace(activeDiffTrace);
    }
  }, [hasLoadedDiffTrace, activeDiffTrace]);

  const {
    isSearchOpen,
    setSearchOpen,
    searchQuery,
    searchResultFields,
    resultIndex,
    selectedSpanIds,
    setSelectedSpanIds,
  } = useTraceSearch();

  useTraceSearchQuery({
    primarySearchParams:
      dynamicObjectId && primaryTrace?.root.root_span_id
        ? {
            objectType,
            objectId: dynamicObjectId,
            rootSpanId: primaryTrace.root.root_span_id,
          }
        : undefined,
    comparisonSearchParams:
      comparisonRowParams.dynamicObjectId &&
      comparisonTracePreview?.root.root_span_id
        ? {
            objectType,
            objectId: comparisonRowParams.dynamicObjectId,
            rootSpanId: comparisonTracePreview.root.root_span_id,
          }
        : undefined,
    spanIdsMap,
  });

  const [isAttachmentBrowserOpen] = useAttachmentBrowser();

  useTraceShortcuts({
    trace: primaryTrace,
    selectedSpan,
    setActiveSpan,
  });

  const { auditLogData } = useRowAuditLog({
    auditLogScan: expandedRowParams.auditLogScan,
    auditLogReady: expandedRowParams.auditLogScanReady,
    rowId: selectedSpan?.id ?? null,
    dynamicObjectId,
    objectType,
  });

  const [collapseSpanState, setCollapseSpanState] = useState<
    "collapsed" | "expanded" | "mixed"
    // important - do not set an initial collapse state
    // because it will override the local storage state
  >();
  const resetForcedSpanCollapse = useEvent(() => setCollapseSpanState("mixed"));

  const [humanReviewState, setHumanReviewState] = useHumanReviewState();
  const isHumanReviewExpanded = humanReviewState === "1";

  useEffect(() => {
    // when we enter human review mode, select the root span
    if (!isHumanReviewExpanded || !trace?.root.id) return;
    setActiveRowAndSpan({ r: trace.root.id, s: trace.root.span_id });
  }, [isHumanReviewExpanded, trace?.root, setActiveRowAndSpan]);

  const tagsObject = trace?.root.data.tags;

  const tagSection = projectConfig && trace?.root?.data && !isReadOnly && (
    <>
      {
        // DEPRECATION_NOTICE:
        // Somewhat subtley, undefined means that the api did not send up tags,
        // and null means that the api sent up an empty value.
        // We can remove this check once everyone updates their API.
        tagsObject !== undefined ? (
          <Tags
            buttonVariant="ghost"
            tags={tagsObject}
            xactId={trace.root.data[TRANSACTION_ID_FIELD]}
            updateTag={
              updateRow &&
              (async (newTags) => {
                const flatSpan = flattenDiffObjects(trace.root.data);
                return await updateRow(flatSpan, ["tags"], newTags);
              })
            }
            rowId={trace.root.data.id}
          />
        ) : (
          <div className="ml-4 w-[300px] text-sm">
            Please upgrade your stack to get access to tags
          </div>
        )
      }
    </>
  );

  const hasError =
    !isEmpty(selectedSpan?.data.error) &&
    (!isDiffObject(selectedSpan?.data.error) ||
      !isEmpty(selectedSpan?.data.error[DiffRightField]));

  const currentTraceViewers = useMemo(() => {
    if (!trace) {
      return null;
    }
    return roster.filter(
      (u) =>
        u.sessions.some((session) => session.row?.id === trace.root.data.id) &&
        u.user_id !== user?.id,
    );
  }, [roster, trace, user?.id]);

  const currentViewersSection = currentTraceViewers && (
    <RosterAvatars roster={currentTraceViewers} context="trace" />
  );

  const [traceTreeCollapsedState, setTraceTreeCollapsed] = useEntityStorage({
    entityType: "traceTree",
    entityIdentifier: projectId ?? "",
    key: "isCollapsed",
  });

  const [manualConfirmationResolve, setManualConfirmationResolve] = useState<
    ((confirmed: boolean) => void) | null
  >(null);

  const { enableScope, disableScope } = useHotkeysContext();

  const receiveManualConfirmation = useCallback(async () => {
    enableScope(HotkeyScope.ConfirmationModal);
    return new Promise((resolve) => {
      setManualConfirmationResolve(
        // Have to wrap in two callbacks, because if we try to set the state
        // directly as a function, react will think it's a callback
        (_: ((confirmed: boolean) => void) | null) => {
          return (confirmed: boolean) => {
            resolve(confirmed);
          };
        },
      );
    });
  }, [enableScope]);

  const containerRef = useRef<HTMLDivElement>(null);
  const minTreePanelWidth = usePanelSize(
    200,
    // eslint-disable-next-line react-compiler/react-compiler
    containerRef.current ?? undefined,
  );
  const maxTreePanelWidth = usePanelSize(
    400,
    // eslint-disable-next-line react-compiler/react-compiler
    containerRef.current ?? undefined,
  );
  const spanPanelMinSize = usePanelSize(280);
  const attachmentPanelMinSize = usePanelSize(280);

  const onBulkAddToDataset = useCallback(
    (dataset: Dataset, spanIds: string[]) => {
      if (!selectedSpanIds.size) return;
      const traceSpans = Object.values(trace?.spans ?? {});

      const selectedSpans = spanIds
        .map((id) => {
          return traceSpans.find((s) => s.id === id);
        })
        .filter((s) => s !== undefined);

      performAddRowsToDataset({
        datasetId: dataset.id,
        datasetName: dataset.name,
        spans: selectedSpans,
      });

      setSelectedSpanIds(new Set());
    },
    [selectedSpanIds, trace, performAddRowsToDataset, setSelectedSpanIds],
  );

  const { expandedFrame, setExpandedFrame, parsedSpanData } =
    useExpandedSpanIframe({
      objectType,
      spanIframes: projectConfig?.span_iframes,
      selectedSpan,
    });

  const {
    expectedData,
    autoScores,
    manualScores,
    numExpectedScores,
    oldestExpectedScore,
  } = useExtractScores({
    objectType,
    projectConfigScores: projectConfig?.scores,
    span: selectedSpan,
  });

  const queryClient = useQueryClient();
  const onUpdateManualReviewScore = useEvent(
    async (updates: ManualScoreUpdate[]) => {
      if (!batchUpdateRow) return [];

      const spanIdToRowUpdates: Map<
        string,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        { path: string[]; newValue: any }[]
      > = new Map();
      const spanIdToRightData: Map<string, Record<string, unknown>> = new Map();

      for (const { name, category, value } of updates) {
        const score = manualScores[name];
        const span =
          score && trace && score.spanId && trace.spans[score.spanId];
        if (!span) {
          throw new Error("Unknown score " + name);
        }

        if (!spanIdToRightData.has(span.id)) {
          const rightData = Object.fromEntries(
            Object.entries(span.data).map(([k, v]) => [
              k,
              isDiffObject(v) ? v[DiffRightField] : v,
            ]),
          );
          spanIdToRightData.set(span.id, rightData);
        }

        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        let rowUpdates: { path: string[]; newValue: any }[];
        if (score.config.config?.destination === "expected") {
          // Since in the single human review score case we write to expected as a flat value,
          // when another human review score is added, we may have to guess what the existing
          // expected score referred to when we make updates
          // There are 4 cases:
          // numExpectedScores === 1
          //   - The existing value is a string | string[] | null: we should overwrite it
          //   - The existing value is a { [category: string]: string | string[] | null }: we
          //     should inject the new value into the category
          // numExpectedScores > 1
          //  - The existing value is a string | string[] | null: we should assume that this value
          //    corresponds to the first category (by timestamp), and inject the new value into the
          //    second category, resulting in { [category]: string | string[] | null }.
          //    If the value does not match any existing category, we inject it into the "expected" key
          //  - The existing value is a { [category: string]: string | string[] | null }: we
          //    should inject the new value into the category
          if (numExpectedScores === 1 && isObject(expectedData)) {
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: category ?? undefined,
              },
            ];
          } else if (numExpectedScores === 1) {
            const expectedCategories = Array.isArray(expectedData)
              ? expectedData
              : [expectedData];
            const allMatch = expectedCategories.every(
              (category) =>
                score.config.score_type === "categorical" &&
                score.config.categories.find((s) => s.name === category),
            );

            const confirmed =
              allMatch ||
              isEmpty(expectedData) ||
              (await receiveManualConfirmation());
            if (confirmed) {
              // filter out categories that don't match the score config
              const allowedCategories =
                Array.isArray(category) &&
                score.config.score_type === "categorical" &&
                score.config.categories;
              const categoryMatches = allowedCategories
                ? category.filter(
                    (c) => !!allowedCategories.find((s) => s.name === c),
                  )
                : category;

              rowUpdates = [
                {
                  path: ["expected"],
                  newValue: categoryMatches,
                },
              ];
            } else {
              continue;
            }
          } else if (numExpectedScores > 1 && isObject(expectedData)) {
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: category,
              },
            ];
          } else if (numExpectedScores > 1) {
            const existingDataArray = Array.isArray(expectedData)
              ? expectedData
              : [expectedData];
            const isValidExistingScore =
              oldestExpectedScore?.score_type === "categorical"
                ? existingDataArray.some((d) =>
                    oldestExpectedScore.categories.some((s) => s.name === d),
                  )
                : null;
            rowUpdates = [
              ...(oldestExpectedScore?.name === name
                ? []
                : isValidExistingScore && oldestExpectedScore
                  ? [
                      {
                        path: ["expected", oldestExpectedScore.name],
                        newValue: expectedData,
                      },
                    ]
                  : expectedData != null
                    ? [
                        {
                          path: ["expected", "expected"],
                          newValue: expectedData,
                        },
                      ]
                    : []),
              {
                path: ["expected", name],
                newValue: category,
              },
            ];
          } else {
            console.assert(false, "Unexpected number of expected scores");
            rowUpdates = [
              {
                path: ["expected", name],
                newValue: value,
              },
            ];
          }
        } else if (score.config.score_type === "free-form") {
          rowUpdates = [
            {
              path: freeFormDataPath({
                scoreType: score.config.score_type,
                destination: score.config.config?.destination,
                name: score.config.name,
              }),
              newValue: value,
            },
          ];
        } else {
          rowUpdates = [
            {
              path: ["scores", name],
              newValue: value,
            },
          ];
        }

        if (!spanIdToRowUpdates.has(span.id)) {
          spanIdToRowUpdates.set(span.id, []);
        }
        spanIdToRowUpdates.get(span.id)!.push(...rowUpdates);
      }

      return await Promise.all(
        Array.from(spanIdToRowUpdates.entries()).map(
          async ([spanId, rowUpdates]) => {
            const result = await batchUpdateRow(
              spanIdToRightData.get(spanId)!,
              rowUpdates,
            );

            queryClient.invalidateQueries({
              queryKey: [
                "fullSpan",
                objectType,
                dynamicObjectId,
                projectId,
                spanId,
              ],
            });
            return result;
          },
        ),
      );
    },
  );

  const scrollableContainerRef = useRef<HTMLDivElement>(null);
  const expandedFrameRef = useRef<HTMLIFrameElement>(null);

  const isDatasetRow = objectType === "dataset";

  const traceSearchVirtualizer = useRef<Virtualizer<
    HTMLDivElement,
    Element
  > | null>(null);

  const { setFullscreenState } = useTraceFullscreen();

  // If in human review mode, show a loading dialog when we are loading the trace,
  // and we don't yet know if the trace is missing
  const isHumanReviewStillLoading = !trace || !selectedSpan;
  if (isHumanReviewExpanded && isHumanReviewStillLoading) {
    return (
      <div className="flex flex-col gap-1 px-4 pt-2">
        <Skeleton className="mb-2 h-10" />
        <Skeleton className="h-64" />
        <Skeleton className="h-64" />

        <Dialog open>
          <DialogContent hideCloseButton>
            <VisuallyHidden>
              <DialogTitle>Loading</DialogTitle>
            </VisuallyHidden>
            <div className="flex items-center gap-3 text-sm">
              <Spinner className="size-3" />
              Loading human review
            </div>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // If human review is not expanded, show a loading state when we are loading the primary row
  // before we know if we have a trace or not
  // eslint-disable-next-line react-compiler/react-compiler
  if (
    !isHumanReviewExpanded &&
    (isPrimaryTracePending ||
      isPrimaryRowPending ||
      !activeLoadedTrace ||
      !getSpanBySpanId({
        spanId: selectedSpan?.data.span_id ?? null,
        spanIdsMap,
        spans: primaryTrace?.spans,
      }))
  ) {
    return (
      <div className="flex flex-col gap-1 px-4 pt-2">
        <div className="mb-2 flex items-center gap-2 ">
          <Skeleton className="h-8 grow" />
          <Button
            size="xs"
            onClick={() => {
              onClose();
              setFullscreenState(null);
            }}
            Icon={XIcon}
          />
        </div>
        <Skeleton className="h-64" />
        <Skeleton className="h-64" />
      </div>
    );
  }

  if (!trace) {
    const isProcessing =
      isRealtimeStateExhausted(traceRealtimeState) ||
      isRealtimeStateExhausted(spanRealtimeState);
    return (
      <>
        <TraceHeader
          onClose={onClose}
          onPrevRow={onPrevRow}
          onNextRow={onNextRow}
          rowId={primaryRowId}
          onToggleSearch={() => {
            setViewType("trace");
          }}
        />
        <div className="flex flex-1 p-4">
          <TableEmptyState
            label={
              isProcessing
                ? `This row is being processed. Please check back again soon.`
                : `No row found with id ${primaryRowId}`
            }
            labelClassName="text-sm"
            className="flex-1 justify-center"
            Icon={isProcessing ? Hourglass : SearchSlash}
          />
        </div>
      </>
    );
  }

  // If the trace tree state has not been manually set, and the trace has no children, collapse it
  const isTraceTreeCollapsed =
    trace &&
    traceTreeCollapsedState === null &&
    trace.root.children.length === 0
      ? true
      : traceTreeCollapsedState;

  const comparisonClassName =
    EXPERIMENT_COMPARISON_COLOR_CLASSNAMES[comparisonExperimentIndex];

  const spanContents = (opts: {
    shouldRenderManualScores?: boolean;
    defaultCollapsedFields?: SpanField[];
    shouldRenderSpanDetails?: boolean;
  }) => {
    if (!selectedSpan || !trace) return null;
    return (
      <>
        {/* TODO: Move props to context. */}
        {expandedFrame ? (
          <IFrameViewer
            key={selectedSpan.id}
            urlTemplate={expandedFrame.url}
            postMessage={expandedFrame.post_message ?? false}
            value={parsedSpanData}
            className="flex-1"
            iframeRef={expandedFrameRef}
            onUpdate={(field, data) => {
              updateRow?.(selectedSpan.data, field.split("."), data);
            }}
          />
        ) : (
          <SpanContents
            isDatasetRow={isDatasetRow}
            isRoot={trace?.root.id === selectedSpan.id}
            span={selectedSpan}
            hasError={hasError}
            trace={trace}
            projectConfig={projectConfig}
            onApplySearch={onApplySearch}
            autoScores={autoScores}
            copilotContext={copilotContext}
            comparisonClassName={comparisonClassName}
            resetForcedSpanCollapse={resetForcedSpanCollapse}
            resultIndex={resultIndex}
            searchResultFields={searchResultFields}
            searchQuery={searchQuery}
            objectType={objectType}
            editableFields={editableFields}
            hiddenFields={hiddenFields}
            defaultCollapsedFields={opts.defaultCollapsedFields}
            onUpdateManualReviewScore={onUpdateManualReviewScore}
            shouldRenderSpanDetails={opts.shouldRenderSpanDetails}
            shouldRenderManualScores={opts.shouldRenderManualScores}
            commentFn={
              playXProps && objectType === "dataset"
                ? playXProps.datasetDml.commentFn
                : traceViewParams.commentFn
            }
            deleteCommentFn={
              playXProps && objectType === "dataset"
                ? playXProps.datasetDml.deleteCommentFn
                : traceViewParams.deleteCommentFn
            }
            collapseSpanState={collapseSpanState}
            manualScores={manualScores}
            experimentNames={experimentNames}
            updateRow={updateRow}
            customColumns={expandedRowParams.customColumnsParams?.customColumns}
            auditLogData={auditLogData}
            allAvailableModelCosts={expandedRowParams.allAvailableModelCosts}
            scrollTo={
              !scrollToSpanId || scrollToSpanId === selectedSpan.id
                ? scrollTo
                : undefined
            }
          />
        )}
      </>
    );
  };

  const spanName = (
    <SpanName
      hasError={hasError}
      resultIndex={resultIndex}
      searchResultFields={searchResultFields}
      span={selectedSpan}
    />
  );

  const parentId = selectedSpan?.parent_span_id;
  const parentSpanType = parentId
    ? trace?.spans[parentId]?.data.span_attributes?.type
    : null;

  const showSearchResults =
    !isDatasetRow && isSearchOpen && searchQuery !== "" && selectedSpan;

  const traceContent = !trace ? (
    <div className="flex flex-1 p-4">
      <TableEmptyState
        label={`No row found with id ${primaryRowId}`}
        labelClassName="text-sm"
        className="flex-1 justify-center"
        Icon={SearchSlash}
      />
    </div>
  ) : (
    <>
      {isSearchOpen && selectedSpan && (
        <PaginatedTraceSearch
          virtualizerRef={traceSearchVirtualizer}
          isDataset={isDatasetRow}
          spans={primaryTrace?.spans}
          comparisonSpans={comparisonTracePreview?.spans}
          onGoToField={({ span, field }) => {
            if (span.id !== selectedSpan.id) {
              setSelectedSpan(span);
            }
            setScrollTo({ spanId: span.id, v: field });
          }}
          selectedSpans={{
            primary: primarySelectedSpan?.data,
            comparison: comparisonSelectedSpan?.data,
          }}
        />
      )}
      <RowComparisonSelection
        rowId={rowId}
        selectableExperimentsList={selectableExperimentsList}
        selectedExperimentId={comparisonExperiment?.id ?? undefined}
        onExperimentSelected={(id) => {
          setComparisonExperimentId(id);
        }}
        comparisonClassName={comparisonClassName}
        comparableRowIds={comparisonRowIds}
        comparisonRowId={comparisonRowId ?? ""}
        onCompareRowIdSelected={(id) => {
          const comparisonExperimentId = comparisonExperiment?.id;
          if (comparisonExperimentId) {
            setSelectedComparisonParams((prev) => ({
              ...prev,
              [comparisonKey]: {
                [comparisonExperimentId]: id,
              },
            }));
          }
        }}
      />
      {isDiffModeRowId(rowId) &&
        !isPrimaryRowPending &&
        !comparisonRowId &&
        (!isComparisonRowQueryEnabled || !isComparisonRowsPending) && (
          <div className="flex h-10 items-center gap-2 border-b px-4 text-xs text-primary-500">
            <TriangleAlert className="size-3" />
            There are no input matches in the comparison experiment. Diffs will
            not be shown for this row.
          </div>
        )}
      {!isHumanReviewExpanded && (
        <div className="flex-1 overflow-hidden @container" ref={containerRef}>
          <ResizablePanelGroup
            direction="horizontal"
            autoSaveId="tracePanel"
            className="flex flex-1 overflow-hidden"
            debugProps={{
              windowInnerWidth: window.innerWidth,
              minSize: minTreePanelWidth,
              maxSize: maxTreePanelWidth,
            }}
          >
            {(showSearchResults ||
              (!isDatasetRow && !isTraceTreeCollapsed)) && (
              <>
                <ResizablePanel
                  order={0}
                  className="relative flex"
                  minSize={minTreePanelWidth}
                  maxSize={viewType !== "trace" ? undefined : maxTreePanelWidth}
                  id="tree"
                >
                  <CollapsibleTraceTree
                    showLayoutDropdown
                    comparisonClassName={comparisonClassName}
                    firstRootTitle={traceViewParams.title}
                    searchResults={
                      showSearchResults && (
                        <TraceSearchResults
                          virtualizerRef={traceSearchVirtualizer}
                          openCreateDatasetDialog={() =>
                            openCreateDatasetDialog("Untitled")
                          }
                          onBulkAddToDataset={onBulkAddToDataset}
                          spans={primaryTrace?.spans}
                          comparisonSpans={comparisonTracePreview?.spans}
                          comparisonExperimentIndex={
                            comparisonExperiment
                              ? comparisonExperimentIndex
                              : undefined
                          }
                          onGoToField={({ span, field }) => {
                            if (span.id !== selectedSpan.id) {
                              setSelectedSpan(span);
                            }
                            setScrollTo({ spanId: span.id, v: field });
                          }}
                          spanIdsMap={spanIdsMap}
                        />
                      )
                    }
                    selectedSpan={selectedSpan}
                    setSelectedSpan={setSelectedSpan}
                    setTraceTreeCollapsed={setTraceTreeCollapsed}
                    trace={selectedPreviewTrace}
                    objectType={objectType}
                    objectId={objectId}
                    allAvailableModelCosts={
                      expandedRowParams.allAvailableModelCosts
                    }
                  />
                </ResizablePanel>
                {viewType === "trace" && (
                  <ResizableHandle className="bg-primary-200/70" />
                )}
              </>
            )}
            <ResizablePanel
              order={1}
              minSize={spanPanelMinSize}
              className={cn("flex relative", { hidden: viewType !== "trace" })}
              id="span"
            >
              <ScrollableContainerWithOptions
                containerRef={scrollableContainerRef}
                className="relative flex flex-1 flex-col overflow-y-auto px-4 py-3 @container"
              >
                {selectedSpan && (
                  <div className="flex flex-1 flex-col gap-3.5 text-sm">
                    {isDatasetRow ? (
                      <div className="mb-2 text-base font-semibold">
                        Edit dataset row {rowIndex}
                      </div>
                    ) : (
                      <SpanHeader
                        isTraceTreeCollapsed={isTraceTreeCollapsed}
                        isSearchOpen={isSearchOpen}
                        searchQuery={searchQuery}
                        span={selectedSpan}
                        isReadOnly={isReadOnly}
                        performAddRowsToDataset={performAddRowsToDataset}
                        collapseSpanState={collapseSpanState}
                        setCollapseSpanState={setCollapseSpanState}
                        spanName={spanName}
                        copilotContext={copilotContext}
                        openCreateDatasetDialog={openCreateDatasetDialog}
                        expandedFrame={expandedFrame}
                        setExpandedFrame={setExpandedFrame}
                        expandedFrameRef={expandedFrameRef}
                        parsedSpanData={parsedSpanData}
                        parentSpanType={parentSpanType}
                        isPending={isTransitioningToNewSpan}
                      />
                    )}
                    {spanContents({
                      shouldRenderManualScores: !playXProps,
                      shouldRenderSpanDetails: true,
                    })}
                  </div>
                )}
              </ScrollableContainerWithOptions>
            </ResizablePanel>
            {isAttachmentBrowserOpen && selectedSpan && (
              <>
                <ResizableHandle className="hidden bg-primary-200/70 @4xl:flex" />
                <ResizablePanel
                  order={2}
                  id="attachments"
                  className="hidden flex-col @4xl:flex"
                  minSize={attachmentPanelMinSize}
                >
                  <AttachmentBrowser spanData={selectedSpan.data} />
                </ResizablePanel>
              </>
            )}
          </ResizablePanelGroup>
        </div>
      )}
      <ExpandedHumanReviewModal
        savingState={traceViewParams.savingState}
        isDatasetRow={isDatasetRow}
        expandedRowState={expandedRowState}
        actions={
          <>
            <div className="flex flex-wrap items-center gap-1">
              {tagsObject
                ?.filter((t) => !RESERVED_TAGS.includes(t))
                .map((tag, idx) => {
                  const tagConfig = projectConfig.tags.find(
                    (t) => t.name === tag,
                  );
                  return (
                    <Tag
                      key={idx}
                      label={tagConfig?.name ?? tag}
                      color={tagConfig?.color}
                      description={tagConfig?.description ?? undefined}
                    />
                  );
                })}
            </div>
            {tagSection}
            {expandedFrame && (
              <>
                <Button
                  Icon={Minimize2}
                  size="xs"
                  onClick={() => setExpandedFrame(null)}
                >
                  Minimize span iframe
                </Button>
                <RefreshIframeButton
                  spanIframe={expandedFrame}
                  expandedFrameRef={expandedFrameRef}
                  parsedSpanData={parsedSpanData}
                  variant="border"
                />
              </>
            )}
          </>
        }
        rowIndex={rowIndex}
        totalRows={totalRows}
        open={isHumanReviewExpanded}
        onOpenChange={(open) => {
          setHumanReviewState(open ? "1" : null);
        }}
        onPrevRow={onPrevRow}
        onNextRow={onNextRow}
        onJumpToRow={onJumpToRow}
        updateScores={onUpdateManualReviewScore}
        scores={manualScores}
        rowKey={selectedSpan?.data.id}
        xactId={
          /* XXX Should create one for the score */
          selectedSpan?.data[TRANSACTION_ID_FIELD] ?? null
        }
        currentTraceViewers={currentTraceViewers}
      >
        {spanName}
        {spanContents({
          shouldRenderManualScores: false,
          defaultCollapsedFields: HUMAN_REVIEW_DEFAULT_COLLAPSED_FIELDS,
        })}
      </ExpandedHumanReviewModal>
      {createDatasetModal}
      {manualConfirmationResolve && (
        <ConfirmationDialog
          open={!!manualConfirmationResolve}
          onOpenChange={(open) => {
            setManualConfirmationResolve(
              (prev: ((confirmed: boolean) => void) | null) => {
                if (!open && prev) {
                  prev(false);
                  return null;
                } else {
                  return prev;
                }
              },
            );
            if (!open) {
              disableScope(HotkeyScope.ConfirmationModal);
            }
          }}
          title="Overwrite expected field"
          confirmText="Overwrite"
          onConfirm={() => {
            manualConfirmationResolve(true);
            setManualConfirmationResolve(null);
          }}
        >
          The expected field is not empty. By setting this score, the expected
          field will be replaced by the score value. Are you sure you want to
          set the score as the expected value?
        </ConfirmationDialog>
      )}
    </>
  );

  return (
    <>
      <TraceHeader
        isDataset={isDatasetRow}
        onClose={onClose}
        onPrevRow={onPrevRow}
        onNextRow={onNextRow}
        rowId={trace?.root.id}
        onToggleSearch={() => {
          setSearchOpen(!isSearchOpen);
          setViewType("trace");
        }}
      >
        {currentViewersSection}
        {tagSection}
      </TraceHeader>
      <GoToOriginProvider origin={selectedSpan?.data.origin}>
        {traceContent}
      </GoToOriginProvider>
    </>
  );
}

function isRealtimeStateExhausted(realtimeState: RealtimeState | undefined) {
  return realtimeState?.type && realtimeState.type.includes("exhausted");
}

export type traceCollapseState =
  | { state: "expanded" }
  | { state: "collapsed"; ids: Set<string> }
  | { state: "mixed"; ids: Set<string> };

export function CollapsibleTraceTree({
  comparisonClassName,
  firstRootTitle,
  searchResults,
  selectedSpan,
  setSelectedSpan,
  setTraceTreeCollapsed,
  trace,
  objectType,
  objectId,
  allAvailableModelCosts,
  label = "Trace",
  showLayoutDropdown,
}: {
  comparisonClassName: string;
  firstRootTitle?: string;
  searchResults?: React.ReactNode;
  selectedSpan: Span | null;
  setSelectedSpan: (span: Span) => void;
  setTraceTreeCollapsed: (isCollapsed: boolean) => void;
  trace: Trace | null;
  objectType: DataObjectType;
  objectId: string | undefined;
  allAvailableModelCosts?: Record<string, ModelCosts>;
  label?: string;
  showLayoutDropdown?: boolean;
}) {
  const [viewType] = useTraceViewTypeState();
  const isTimelineView = viewType === "timeline";
  const isThreadView = viewType === "thread";

  const [collapseState, setCollapseState] = useState<traceCollapseState>({
    state: "expanded",
  });
  const collapseTraceHandler = useCallback(() => {
    if (collapseState.state === "mixed" || collapseState.state === "expanded") {
      if (!trace) return;

      const allIds = new Set<string>();
      const collectIds = (spans: Span[]) => {
        spans.forEach((span) => {
          if (span.children.length > 0) {
            allIds.add(span.id);
            collectIds(span.children);
          }
        });
      };
      collectIds([trace.root]);

      setCollapseState({ state: "collapsed", ids: allIds });
    } else if (collapseState.state === "collapsed") {
      setCollapseState({ state: "expanded" });
    }
  }, [collapseState, trace]);

  const [shouldShowMetrics, setShouldShowMetricsState] = useState(true);
  const canShowMetrics = !!trace && !isEmpty(trace.root.data.metrics);
  const showMetrics = shouldShowMetrics && canShowMetrics;

  const flattenedRootData = flattenDiffObjects(trace?.root.data);
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const traceStart = flattenedRootData?.metrics?.start as number | undefined;
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  const traceEnd = flattenedRootData?.metrics?.end as number | undefined;

  const containerRef = useRef<HTMLDivElement>(null);

  // Conditionally render the trace tree within this component, so that state (like collapseState) is preserved
  // when traces change and are potentially null during the change.
  if (!trace) return null;
  if (searchResults) return searchResults;

  return (
    <ScrollableContainerWithOptions
      className="flex-1 overflow-y-auto pb-10 pt-3"
      containerRef={containerRef}
      enableScrollToBottom={isThreadView}
      enableScrollToTop={false}
      hideOptions
    >
      <div className="flex items-center gap-0.5 px-3 pb-3 bg-background">
        <div className="grow text-xs">
          {showLayoutDropdown ? <TraceLayoutDropdown label={label} /> : label}
        </div>
        {canShowMetrics && !isTimelineView && !isThreadView && (
          <ActionButton
            hotkey="m"
            buttonVariant="ghost"
            actionHandler={() => setShouldShowMetricsState((prev) => !prev)}
            tooltipText={`${showMetrics ? "Hide" : "Show"} metrics`}
            className="text-primary-400"
            icon={
              showMetrics ? (
                <Timer className="size-3" />
              ) : (
                <TimerOff className="size-3" />
              )
            }
          />
        )}
        {!isThreadView && (
          <ActionButton
            hotkey="."
            buttonVariant="ghost"
            actionHandler={collapseTraceHandler}
            tooltipText={
              collapseState.state === "mixed" ||
              collapseState.state === "expanded"
                ? `Collapse all`
                : `Expand all`
            }
            className="text-primary-400"
            icon={
              collapseState.state === "mixed" ||
              collapseState.state === "expanded" ? (
                <FoldVertical className="size-3" />
              ) : (
                <UnfoldVertical className="size-3" />
              )
            }
          />
        )}
        {!isTimelineView && !isThreadView && (
          <ActionButton
            key="collapse-trace"
            hotkey="\"
            buttonVariant="ghost"
            actionHandler={() => setTraceTreeCollapsed(true)}
            tooltipText="Hide trace tree"
            className="text-primary-400"
            icon={<ArrowLeftToLine className="size-3" />}
          />
        )}
        {isThreadView && <ThreadSpanMetrics span={trace.root} isRoot />}
      </div>
      <TraceTree
        trace={trace}
        containerRef={containerRef}
        seen={new Set()}
        comparisonClassName={comparisonClassName}
        selectedSpan={selectedSpan}
        setSelectedSpan={setSelectedSpan}
        firstRootTitle={firstRootTitle}
        showMetrics={showMetrics || isTimelineView}
        collapseState={collapseState}
        onCollapseStateChange={setCollapseState}
        totalDuration={
          !isEmpty(traceStart) &&
          !isEmpty(traceEnd) &&
          (showMetrics || isTimelineView)
            ? traceEnd - traceStart
            : undefined
        }
        traceStart={traceStart}
        threadPaginationParams={{
          objectType,
          objectId,
        }}
        allAvailableModelCosts={allAvailableModelCosts}
      />
    </ScrollableContainerWithOptions>
  );
}

// const reference for return value from comparisonExpandedRowParams when scanQueryKeys is undefined
const emptyArray: string[] = [];

function comparisonExpandedRowParams({
  rowId,
  selectedRowId,
  expandedRowParams,
  selectedExperimentIndex,
  makeDynamicObjectId,
  comparisonDynamicObjectId,
}: {
  rowId: TraceProps["rowId"];
  selectedRowId: string | null | undefined;
  expandedRowParams: ExpandedRowParams;
  selectedExperimentIndex: number;
  makeDynamicObjectId: boolean;
  comparisonDynamicObjectId?: string | null;
}) {
  const { multiExperimentData } = expandedRowParams;
  if (!isDiffModeRowId(rowId) || !multiExperimentData) {
    return {
      rowId: null,
      scan: null,
      ready: [],
      channel: null,
      dynamicObjectId: null,
    };
  }

  return {
    rowId:
      selectedRowId ??
      rowId[diffKeynameForIndex(selectedExperimentIndex + 1, true)] ??
      null,
    scan: multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
      ?.scan,
    ready: [
      multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
        ?.refreshed ?? 0,
    ],
    dynamicObjectId: makeDynamicObjectId
      ? (comparisonDynamicObjectId ??
        multiExperimentData.comparisonExperimentData[selectedExperimentIndex]
          ?.id ??
        null)
      : null,
    channel: null,
    customColumnsParams: {
      scan:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.tableScan ?? null,
      scanQueryKeys:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.queryKeys ?? emptyArray,
      customColumns:
        multiExperimentData?.comparisonExperimentData[selectedExperimentIndex]
          ?.customColumns,
    },
  };
}

export interface ManualScoreUpdate {
  name: string;
  category: string | string[] | null;
  value: string | number | null;
}
