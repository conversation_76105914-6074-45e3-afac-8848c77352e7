import { ExperimentSelectionSection } from "#/app/app/[org]/p/[project]/experiments/[experiment]/ExperimentSelectionSection";
import { useObjectSchemaFields } from "#/utils/queries/schema";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { type RowId, makeRowIdPrimary } from "#/utils/diffs/diff-objects";
import * as Query from "#/utils/btql/query-builder";
import type { FormatterMap } from "#/ui/field-to-column";
import {
  type Clause,
  type ClauseChecker,
  type Search,
  addClause,
  buildDefaultOrderBy,
  makeBtqlFullTextFilter,
  makeBrainstoreFullTextFilter,
  makeSimpleMatchFilters,
  makeTagsFilterTree,
  useScoreMetricsTopLevelFields,
} from "#/utils/search/search";
import { type ViewProps, type ViewParams } from "#/utils/view/use-view";
import {
  type TableSelection,
  useTableSelection,
} from "#/ui/table/useTableSelection";
import { VizQuery, type VizQueryProps } from "#/ui/viz-query";
import {
  type DataObjectSearch,
  type DataObjectType,
  buildSearchKey,
  useDataObjects,
} from "#/utils/btapi/btapi";
import {
  type ChannelSpec,
  CreatedField,
  IdField,
  InsertRecordsResult,
  TagsField,
  TransactionIdField,
  type UpdateLog,
  buildScanQuery,
  useDBQuery,
  useDuckDB,
  useParquetViews,
} from "#/utils/duckdb";
import { type DML, useMutableObject } from "#/utils/mutable-object";
import { jsonSerializableDeepEqual } from "#/utils/object";
import { DuckDBTypeHints } from "#/utils/schema";
import { useOrg } from "#/utils/user";
import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { type SavingState } from "#/ui/saving";
import { type ExpandedRowParams, type TraceViewParams } from "#/ui/trace/trace";
import { setTagSearchFn, useHasTags, useTagsFormatter } from "#/ui/trace/tags";
import { z } from "zod";
import { sha1 } from "#/utils/hash";
import { runAISearch } from "#/utils/ai-search/actions/events";
import { ident, singleQuote } from "#/utils/sql-utils";
import { type ClientOptions } from "openai";
import {
  buildDefaultWhere,
  makeBtqlTagsFilter,
  makeFilterClauses,
  type UseClauseCheckerProps,
} from "#/utils/search-btql";
import { useFeatureFlags } from "#/lib/feature-flags";
import { TracePanel } from "#/ui/trace/trace-panel";
import {
  ComputedCostMetricFields,
  ComputedDurationMetricFields,
  ComputedMetricFields,
  ComputedTokenMetricFields,
  constructFullSummaryScan,
  doubleQuote,
  experimentScanSpanSummary,
} from "@braintrust/local/query";
import { type AISearchType } from "@braintrust/local";
import { type Schema, type TypeMap } from "apache-arrow";
import {
  useNavToObjectId,
  useActiveRowAndSpan,
  useIsLiveState,
} from "#/ui/query-parameters";
import { type Roster } from "#/utils/realtime-data";
import useFilterSortBarSearch from "./use-filter-sort-search";
import {
  modelArrowSchema,
  modelTableDefinition,
  useAvailableModels,
} from "./prompts/models";
import { useTempDuckDataTable } from "#/utils/queries/useTempDuckTable";
import { Views } from "#/ui/views";
import { useBtql, useIsRootAvailable } from "#/utils/btql/btql";
import {
  type ParsedQuery,
  Parser,
  type Expr as ParsedExpr,
  type LiteralValue,
} from "@braintrust/btql/parser";
import AppliedFilters from "./applied-filters";
import { SpanTypeInfoFormatter } from "./table/formatters/span-info-formatter";
import { PercentWithAggregationFormatter } from "./table/formatters/percent-formatter";
import { makeFormatterMap } from "./table/formatters/header-formatters";
import { ErrorCellWithFormatter } from "./table/formatters/error-formatter";
import { useObjectIdResolver } from "./use-object-id-resolver";
import { GroupKeyFormatterWithCell } from "./table/formatters/group-key-formatter";
import { InputFormatter } from "./table/formatters/input-formatter";
import {
  GROUP_BY_NONE_VALUE,
  type SelectionType,
} from "./charts/selectionTypes";
import {
  BT_GROUP_KEY,
  nullValueExpr,
  rowGroupingQuery,
} from "./table/grouping/queries";
import { useTableGroupingControl } from "./table/grouping/controls";
import { DefaultWithAggregationFormatter } from "./table/formatters/default-formatter";
import { DurationWithAggregationFormatter } from "./table/formatters/duration-formatter";
import { CostWithAggregationFormatter } from "./table/formatters/cost-formatter";
import { CurlyBraces } from "lucide-react";
import {
  parseCustomColumnsSchema,
  useCustomColumns,
} from "#/utils/custom-columns/use-custom-columns";
import {
  type RowData,
  Table as TableUI,
  type CustomColumnProps,
} from "./arrow-table";
import { makePreviewBtqlReplace } from "#/utils/btapi/fetch";
import {
  AdvancedSearchComponents,
  useAdvancedSearchState,
} from "./advanced-search-components";
import { StartEndFormatter } from "./table/formatters/start-end-formatter";
import useEvent from "react-use-event-hook";
import { useQuery } from "@tanstack/react-query";
import { useAPIVersion } from "./api-version/check-api-version";
import { type SummaryPaginatedObjectViewerTableQuery } from "./summary-paginated-object-viewer";
import { InfoBanner } from "./info-banner";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";
import { LiveButton } from "./live-button";
import { TIME_RANGE_TO_INTERVAL } from "#/app/app/[org]/monitor/time-controls/time-range";
import { parseDateString } from "./logs-time-range";
import { endOfDay } from "date-fns";

// MaxLimit is the maximum limit we'll try to fetch from the backend. We double
// the limit when adding a page if the previous page did not contribute any new
// rows.
export const MaxLimit = 10000;

// If true, enable debugging hooks.
const DebugMode = false;

export const paginationStateSchema = z.object({
  max_xact_id: z.string().optional(),
  max_root_span_id: z.string().optional(),
  cursor: z.string().optional(),
});

type PaginationState = z.infer<typeof paginationStateSchema>;

export type PageParams = PaginationState & {
  limit?: number;
  min_pagination_key?: string;
  last_page_value?: LiteralValue;
};

export function buildChannelSpecs(
  objectType: DataObjectType,
  searches: DataObjectSearch[],
): Record<string, ChannelSpec> {
  return Object.fromEntries(
    searches.map((search) => [
      buildSearchKey(search),
      {
        objectType: objectType,
        id: search.id,
        shape: "traces",
        audit_log: search.audit_log ?? false,
      },
    ]),
  );
}

export function computeSearchesHash(
  searches: DataObjectSearch[],
): string | undefined {
  return searches.length
    ? sha1(searches.map(buildSearchKey).join(","))
    : undefined;
}

export function getSingularChannel(
  filenames: string[],
  filenameToChannel: (filename: string) => {
    channel: UpdateLog | undefined;
    roster: Roster | undefined;
  },
): { channel: UpdateLog; roster: Roster } | null {
  const channels = filenames.reduce((acc, fname) => {
    const channel = filenameToChannel(fname);
    return channel.channel
      ? // Typescript can't infer that from just channel that channel.channel is not undefined
        acc.concat([{ channel: channel.channel, roster: channel.roster }])
      : acc;
  }, new Array<{ channel: UpdateLog; roster: Roster | undefined }>());
  if (channels.length === 0) {
    return null;
  } else {
    if (
      !channels
        .slice(1)
        .every((log) => Object.is(log.channel, channels[0].channel))
    ) {
      throw new Error(
        "Expected at most one channel spawned for paginated objects view",
      );
    }
    return { channel: channels[0].channel, roster: channels[0].roster ?? [] };
  }
}

export type LoadingStatusType =
  | "pending"
  | "reached_end"
  | "desired_num_traces_unmet"
  | "done_loading";

export type PaginatedObjectViewerDataComponentsArgs = {
  objectType: DataObjectType;
  objectId: string | null;
  objectName: string | null;
  pageSize: number;
  setSavingState: Dispatch<SetStateAction<SavingState>>;
  selectedBucket?: Clause<"filter">;
  traceViewParamArgs?: Partial<TraceViewParams>;
  viewProps: ViewProps;
  viewParams: ViewParams | undefined;
  disableGrouping?: boolean;
  useClauseCheckerProps: UseClauseCheckerProps;
  enableStarColumn?: boolean;
};

type PaginatedObjectViewerTableQuery<TsTable extends RowData, TsValue> = {
  type: "tableQuery";
  query: string | null;
  formatters?: FormatterMap<TsTable, TsValue>;
  signals: number[];
  rowGroupings?: (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rows: any[] | null,
    schema: Schema | undefined,
  ) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    groupRows: any[];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    rowsForGroup: Record<string, any[]>;
    groupColumnName: string;
    groupByMetadataPath: SelectionType;
    groupComparisonIndex?: number;
  } | null;
};

export type PaginatedObjectViewerDataComponents<
  TsTable extends RowData,
  TsValue,
> = {
  objectType: DataObjectType;
  objectId: string | null;
  objectName: string | null;
  pageSize: number;
  scoreFields: string[];
  clauseChecker: ClauseChecker | null;
  viewProps: ViewProps;
  viewParams: ViewParams | undefined;
  pageIdentifier: string;
  search: Search;
  setSearch: React.Dispatch<React.SetStateAction<Search>>;
  hasTags: boolean;
  selectionProps: TableSelection;
  dml: DML;
  loadingStatus: LoadingStatusType;
  initialQueryLoading?: boolean;
  filters: DataObjectSearch["filters"];
  urlRowSearches: DataObjectSearch[];
  rowSchema: Schema<TypeMap> | null;
  rowScanNumTraces: number | undefined;
  rowChannel: UpdateLog | null;
  projectedPaths: string[];
  tableQuery: PaginatedObjectViewerTableQuery<TsTable, TsValue>;
  groupSelectComponent?: React.ReactNode;
  traceViewParams: TraceViewParams;
  setDesiredNumTraces: React.Dispatch<React.SetStateAction<number>>;
  rowDataError: Error | undefined;
  selectedBucket: Clause<"filter"> | undefined;
  customColumns: CustomColumnProps;
  refetchDataQueryFn?: (rowIds?: string[]) => ParsedQuery | undefined;
  newRows: number;
  onRefreshLiveRows?: () => Promise<void>;
};

export function usePaginatedObjectViewerDataComponents<
  TsTable extends RowData,
  TsValue,
>({
  objectType,
  objectId,
  objectName,
  selectedBucket,
  setSavingState,
  traceViewParamArgs,
  pageSize,
  viewProps,
  viewParams,
  disableGrouping,
  useClauseCheckerProps,
  enableStarColumn,
}: PaginatedObjectViewerDataComponentsArgs): PaginatedObjectViewerDataComponents<
  TsTable,
  TsValue
> {
  const org = useOrg();
  const { config: projectConfig, projectSettings } = useContext(ProjectContext);
  const { projectId } = useContext(ProjectContext);

  const {
    flags: {
      disableRealtime: disableRealtimeFlag,
      btqlFastSearch,
      hasIsRootField,
      brainstore,
    },
  } = useFeatureFlags();
  const { version } = useAPIVersion();

  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const [navToObjectId] = useNavToObjectId();

  const extraBubbleFilters = useMemo(
    () => (selectedBucket ? [selectedBucket] : []),
    [selectedBucket],
  );

  const { clauseChecker, setTopLevelFields, setCustomColumnsSchema } =
    useClauseCheckerProps;

  const pageIdentifier = objectType + "-" + objectId;

  const search = useMemo(
    () =>
      (extraBubbleFilters ?? []).reduce(
        (acc, f) => addClause(acc, f),
        viewProps.search,
      ),
    [extraBubbleFilters, viewProps.search],
  );

  const colsObjectType = objectType === "dataset" ? objectType : "project";
  const colsObjectId = objectType === "dataset" ? objectId : projectId;
  const colsSubtype = objectType === "project_logs" ? "project_log" : undefined;
  const {
    customColumnDefinitions,
    customColumnsEnabled,
    createCustomColumn,
    updateCustomColumn,
    deleteCustomColumn,
    isError: customColumnsError,
  } = useCustomColumns({
    objectType: colsObjectType,
    objectId: colsObjectId,
    subtype: colsSubtype,
  });

  const filters = useFilters({
    search,
    brainstore,
    selectedBucket,
    objectType,
    customColumnDefinitions,
    timeSpan: viewProps.timeRangeFilter,
  });

  // Note: keep these initial values in sync with the reset values in
  // `rowPageSearchParams`.
  const [desiredNumTraces, setDesiredNumTraces] = useState<number>(pageSize);
  const [pages, setPages] = useState<PageParams[]>([{}]);

  // The PageParams we accumulate over time are dependent on the exact
  // DataObjectSearches we are conducting, because the pagination cursors will
  // change whenever the search params change. This means we have to reset the
  // pages whenever the search params change.
  const rowPageSearchParams = useMemo(() => {
    // Each time we recompute these, reset the list of pages. Also reset the
    // desired number of traces so we don't try to load too many results if you
    // have already scrolled a lot.
    setPages([{}]);
    setDesiredNumTraces(pageSize);
    return {
      objectId,
      pageSize,
      filters,
      btqlFastSearch,
      objectType,
      hasIsRootField,
    };
  }, [objectId, pageSize, filters, btqlFastSearch, objectType, hasIsRootField]);

  // In addition to paginating rows from the beginning of the object, we allow
  // loading individual rows in parallel to speed up the experience for opening
  // a page with a row id in the URL.
  const [urlRowIdsToFetch, setUrlRowIdsToFetch] = useState<string[]>([]);

  const rowPageSearches = useMemo((): DataObjectSearch[] => {
    const { objectId, pageSize, filters } = rowPageSearchParams;
    return objectId
      ? pages.map((p) => ({
          id: objectId,
          max_xact_id: p.max_xact_id,
          max_root_span_id: p.max_root_span_id,
          limit: p.limit ?? pageSize,
          filters,
          btqlCursor: p.cursor,
          btqlRelaxedSearchMode: rowPageSearchParams.btqlFastSearch,
          audit_log: false,
          replace: makePreviewBtqlReplace({
            objectType: rowPageSearchParams.objectType,
            relaxedSearchMode: rowPageSearchParams.btqlFastSearch,
            hasIsRootField: rowPageSearchParams.hasIsRootField,
            version,
          }),
        }))
      : [];
    // Note: do not let the set of dependencies grow. Instead, add dependencies to
    // `rowPageSearchParams`.
  }, [pages, rowPageSearchParams, version]);
  // Because these searches are not paginated, we can persist them over time.
  const urlRowSearches = useMemo(
    (): DataObjectSearch[] =>
      objectId
        ? urlRowIdsToFetch.map((rowId) => ({
            id: objectId,
            // replace: makePreviewBtqlReplace(objectType),
            filters: {
              sql: [
                {
                  type: "path_lookup",
                  path: [IdField],
                  value: rowId,
                },
              ],
              btql: [
                {
                  op: "eq",
                  left: {
                    btql: "id",
                  },
                  right: {
                    op: "literal",
                    value: rowId,
                  },
                },
              ],
            },
          }))
        : [],
    [objectId, urlRowIdsToFetch],
  );
  const rowSearches = useMemo(
    () => rowPageSearches.concat(urlRowSearches),
    [rowPageSearches, urlRowSearches],
  );

  // Uniquely identifies this version of the row searches, so we can keep track
  // of when our results correspond to the this version of the row searches.
  const rowPageSearchesHash = useMemo(
    () => computeSearchesHash(rowPageSearches),
    [rowPageSearches],
  );
  const rowSearchesHash = useMemo(
    () => computeSearchesHash(rowSearches),
    [rowSearches],
  );

  // We don't actually need the audit log here, except the UpdateLog, so that we can `commentOn`. So
  // we query with the _false_ filter, and then separately fetch the audit data we need in the trace view.
  const auditLogSearches = useMemo(
    () =>
      rowSearches
        .map(
          (m): DataObjectSearch => ({
            ...m,
            audit_log: true,
            replace: {},
            filters: {
              btql: [{ op: "literal", value: false }],
              sql: [],
            },
          }),
        )
        .slice(0, 1),
    [rowSearches],
  );

  const rowFirstSearchKey = useMemo(
    () => (rowSearches.length ? buildSearchKey(rowSearches[0]) : undefined),
    [rowSearches],
  );
  const auditLogFirstSearchKey = useMemo(
    () =>
      auditLogSearches.length ? buildSearchKey(auditLogSearches[0]) : undefined,
    [auditLogSearches],
  );

  const rowChannelSpec = useMemo(
    () => buildChannelSpecs(objectType, rowSearches),
    [objectType, rowSearches],
  );
  const auditLogChannelSpec = useMemo(
    () => buildChannelSpecs(objectType, auditLogSearches),
    [auditLogSearches, objectType],
  );

  const {
    data: rowDataObjects,
    refreshed: rowDataRefreshed,
    error: rowDataError,
  } = useDataObjects({
    objectType: objectType,
    searches: rowSearches,
    disableCache: true,
  });
  useEffect(() => {
    if (rowDataError) {
      console.error("Error loading row data:\n", rowDataError);
    }
  }, [rowDataError]);

  const { data: auditLogDataObjects, refreshed: auditLogDataRefreshed } =
    useDataObjects({
      objectType: objectType,
      searches: auditLogSearches,
      disableCache: true,
    });

  const disableRealtime =
    Boolean(search.match && search.match.length > 0) || disableRealtimeFlag;

  // If there are active filters, then we still want to ingest realtime updates (so that rows
  // in the main table keep getting updated), but we don't want to permit new rows other than
  // those matching the tabular data to show up (b/c they may not match the filters).
  const freezeResultSet = Boolean(search.filter && search.filter.length > 0);

  const {
    fnames: rowFnames,
    schema: rowSchemas,
    refreshed: rowRefresheds,
    filenameToChannel: rowFilenameToChannel,
  } = useParquetViews({
    files: rowDataObjects,
    ready: rowDataRefreshed,
    channelSpecs: rowChannelSpec,
    disableCache: true,
    disableRealtime,
  });

  const {
    fnames: auditLogFnames,
    schema: auditLogSchemas,
    refreshed: auditLogRefresheds,
    filenameToChannel: auditLogFilenameToChannel,
  } = useParquetViews({
    files: auditLogDataObjects,
    ready: auditLogDataRefreshed,
    channelSpecs: auditLogChannelSpec,
    disableCache: true,
    disableRealtime,
  });

  const rowSchema = useMemo(
    () => (rowFirstSearchKey && rowSchemas[rowFirstSearchKey]) || null,
    [rowFirstSearchKey, rowSchemas],
  );
  const auditLogSchema = useMemo(
    () =>
      (auditLogFirstSearchKey && auditLogSchemas[auditLogFirstSearchKey]) ||
      null,
    [auditLogFirstSearchKey, auditLogSchemas],
  );
  // Make sure all the keys from `rowSearches` are present in this array, even
  // if `useParquetViews` hasn't finished updating.
  const rowRefreshed = useMemo(
    () => rowSearches.map((s) => rowRefresheds[buildSearchKey(s)] ?? 0),
    [rowRefresheds, rowSearches],
  );
  const auditLogRefreshed = useMemo(
    () =>
      auditLogSearches.map((s) => auditLogRefresheds[buildSearchKey(s)] ?? 0),
    [auditLogRefresheds, auditLogSearches],
  );
  const rowChannelInfo = useMemo(
    () => getSingularChannel(Object.keys(rowFnames), rowFilenameToChannel),
    [rowFilenameToChannel, rowFnames],
  );
  const rowChannel = rowChannelInfo?.channel ?? null;
  const rowChannelRoster = useMemo(
    () => rowChannelInfo?.roster ?? [],
    [rowChannelInfo],
  );
  const auditLogChannel = useMemo(
    () =>
      getSingularChannel(Object.keys(auditLogFnames), auditLogFilenameToChannel)
        ?.channel ?? null,
    [auditLogFilenameToChannel, auditLogFnames],
  );
  const rowChannelCallback = useCallback(() => rowChannel, [rowChannel]);
  const auditLogChannelCallback = useCallback(
    () => auditLogChannel,
    [auditLogChannel],
  );

  const rowScanRaw = useMemo(
    () =>
      rowSchema &&
      buildScanQuery({
        s: rowSchema,
        fnames: Object.values(rowFnames),
        logName: rowChannel?.tableName,
        disableRealtime,
        freezeResultSet,
      }),
    [
      rowChannel?.tableName,
      rowFnames,
      rowSchema,
      disableRealtime,
      freezeResultSet,
    ],
  );

  const auditLogScanRaw = useMemo(
    () =>
      auditLogSchema &&
      buildScanQuery({
        s: auditLogSchema,
        fnames: Object.values(auditLogFnames),
        logName: auditLogChannel?.tableName,
        disableRealtime,
        freezeResultSet,
      }),
    [
      auditLogChannel?.tableName,
      auditLogFnames,
      auditLogSchema,
      disableRealtime,
      freezeResultSet,
    ],
  );

  // We need to load another page if:
  //
  //    - All pages have been loaded, and all search queries have completed up
  //    to the latest search tag.
  //
  //    - The last loaded page (out of the row page searches only) does not have
  //    zero results, which indicates that we have fetched the whole dataset.
  //
  //    - Any of the following:
  //
  //        - The number of traces in the fully-filtered summary does not equal
  //        the desired number of traces
  //
  // In order to load another page, we add a new page with the pagination cursor
  // from our current row scan.

  const rowAllSearchesLoaded = useMemo(
    () => rowRefreshed.every((r) => r > 0),
    [rowRefreshed],
  );

  const lastRowSearchKey = useMemo(
    () =>
      rowPageSearches.length
        ? buildSearchKey(rowPageSearches[rowPageSearches.length - 1])
        : undefined,
    [rowPageSearches],
  );
  const { data: lastRowSearchLengthResult, loadedTag: lastRowSearchLengthTag } =
    useDBQuery(
      lastRowSearchKey && rowFnames[lastRowSearchKey]
        ? `
      SELECT COUNT(*) c
      FROM
          parquet_scan([${singleQuote(rowFnames[lastRowSearchKey])}])
      `
        : null,
      rowRefreshed,
      {
        loadingTag: rowPageSearchesHash,
      },
    );
  const lastRowSearchLength = useMemo(
    () =>
      z.coerce
        .number()
        .optional()
        .parse(lastRowSearchLengthResult?.toArray()[0]?.toJSON().c),
    [lastRowSearchLengthResult],
  );

  const lastRowSearchCursor = useMemo(() => {
    return lastRowSearchKey && rowDataObjects[lastRowSearchKey]
      ? rowDataObjects[lastRowSearchKey].cursor
      : undefined;
  }, [lastRowSearchKey, rowDataObjects]);

  const rowPageFnames = rowPageSearches.map(
    (s) => rowFnames[buildSearchKey(s)],
  );
  const { data: rowPaginationStateResult, loadedTag: rowPaginationStateTag } =
    useDBQuery(
      rowPageFnames.every((x) => !!x)
        ? `
      SELECT ${TransactionIdField} max_xact_id, root_span_id max_root_span_id
      FROM
          parquet_scan([${rowPageFnames
            .map((fname) => singleQuote(fname))
            .join(", ")}])
      ORDER BY ${TransactionIdField} ASC, root_span_id ASC
      LIMIT 1`
        : null,
      rowRefreshed,
      {
        loadingTag: rowPageSearchesHash,
      },
    );
  const rowPaginationState = useMemo(
    () =>
      paginationStateSchema
        .optional()
        .parse(rowPaginationStateResult?.toArray()[0]?.toJSON()),
    [rowPaginationStateResult],
  );

  const isTraceView = ["experiment", "project_logs"].includes(objectType);

  const {
    scoreFields,
    metadataFields,
    inputFields,
    outputFields,
    expectedFields,
  } = useObjectSchemaFields(
    rowScanRaw,
    rowSchema,
    rowRefreshed,
    projectConfig.scores,
  );

  useScoreMetricsTopLevelFields({
    scoreFields,
    setTopLevelFields,
    include: isTraceView,
  });

  // Note: keep in sync with COLUMN_ORDER in summary-paginated-object-viewer.tsx
  const projectedPaths = useMemo(
    () =>
      [
        "span_type_info",
        TransactionIdField,
        "input",
        "output",
        "error",
        "expected",
        TagsField,
        "scores",
        "metrics",
        "metadata",
        "created",
        "id",
        "span_id",
      ].filter(
        (f) =>
          rowSchema?.fields.find((n) => n.name === f) ||
          (isTraceView && f === "span_type_info"),
      ),
    [isTraceView, rowSchema?.fields],
  );

  // Hardcode "match" to only look at the first (input) field. This avoids us going OOM and lets us send a simple
  // query to the backend to match against it.
  const fullTextSearchPaths = useMemo(() => {
    if (brainstore) {
      // No need to re-run search in the UI
      return [];
    }
    const candidates = projectedPaths.filter((f) =>
      rowSchema?.fields.find((n) => n.name === f),
    );
    return candidates.length > 0
      ? [candidates.find((f) => f === "input") ?? candidates[0]]
      : [];
  }, [brainstore, projectedPaths, rowSchema?.fields]);

  const { modelInserts } = useAvailableModels({ orgName: org.name });
  const { tableName: modelSpecTableName } = useTempDuckDataTable({
    tableNameHashKey: objectId ? `model_specs_${objectType}_${objectId}` : null,
    tableDefinition: modelTableDefinition,
    schema: modelArrowSchema,
    data: modelInserts,
    insertionCategory: "model_costs",
  });

  const modelSpecScan = useMemo(() => {
    return (
      modelSpecTableName && `(SELECT * FROM ${doubleQuote(modelSpecTableName)})`
    );
  }, [modelSpecTableName]);

  // DEPRECATION_NOTICE: Once all customers have upgraded past 0.0.52, we can remove this
  // and the equivalent field from the experiment view.
  const hasErrorField = useMemo(() => {
    return !!rowSchema?.fields.find((f) => f.name === "error");
  }, [rowSchema]);

  const isRootAvailable = useIsRootAvailable();
  const isScanSummary = useMemo(
    () => ["experiment", "project_logs"].includes(objectType),
    [objectType],
  );

  const initialScan = useMemo(() => {
    if (isScanSummary) {
      // NOTE: Now that we load preview values only, we:
      // * Only run post-aggregation filters for summary scans.
      // * Don't re-run plain filters OTHER THAN full text search (which we will be removing soon too)
      const { postAggregation } = makeFilterClauses(search.filter ?? []);
      const plain = makeSimpleMatchFilters(search, fullTextSearchPaths);
      const summary = experimentScanSpanSummary({
        experimentScanRaw: rowScanRaw,
        scoreFields,
        scoreConfig: projectConfig.scores,
        plainFilters: plain,
        postAggregationFilters: postAggregation,
        tags: search.tag?.map((t) => t.text),
        comparisonKey:
          objectType === "experiment"
            ? (projectSettings?.comparison_key ?? null)
            : "id",
        modelSpecScan,
        hasErrorField,
        isRootAvailable,
      });
      return summary;
    } else {
      return rowScanRaw && `SELECT * FROM (${rowScanRaw}) "t"`;
    }
  }, [
    isScanSummary,
    search,
    fullTextSearchPaths,
    rowScanRaw,
    scoreFields,
    projectConfig.scores,
    objectType,
    projectSettings?.comparison_key,
    modelSpecScan,
    hasErrorField,
    isRootAvailable,
  ]);

  const duck = useDuckDB();
  const {
    data: customColumnsTyped,
    error: customParseColumnsError,
    isPending: customColumnsParsePending,
  } = useQuery({
    queryKey: [
      "customColumns",
      duck?.connect,
      isScanSummary,
      rowScanRaw,
      customColumnDefinitions,
      colsObjectType,
      colsSubtype,
      initialScan,
    ],
    queryFn: async ({ signal }) => {
      const conn = await duck!.connect();

      const rowScan = isScanSummary
        ? constructFullSummaryScan({
            experimentScanRaw: rowScanRaw,
            summaryScan: initialScan,
            includeScoresMap: true,
          })
        : initialScan;

      const { customColumns: customColumnsTyped, customColumnsSchema } =
        await parseCustomColumnsSchema({
          conn,
          abort: signal,
          customColumns: customColumnDefinitions,
          objectType: colsObjectType,
          subtype: colsSubtype,
          rowScan,
        });

      return { customColumns: customColumnsTyped, customColumnsSchema };
    },
    throwOnError: false,
    enabled: !!duck && !!initialScan,
  });

  useEffect(() => {
    if (customColumnsParsePending) {
      return;
    }
    if (customColumnsError || customParseColumnsError) {
      setCustomColumnsSchema(null);
      return;
    }
    setCustomColumnsSchema(customColumnsTyped?.customColumnsSchema ?? null);
  }, [
    setCustomColumnsSchema,
    customColumnsParsePending,
    customColumnsTyped?.customColumnsSchema,
    customColumnsError,
    customParseColumnsError,
  ]);

  const projectedWithCustom = useMemo(
    () =>
      projectedPaths.concat(
        (customColumnsTyped?.customColumns || []).map(({ name }) =>
          doubleQuote(name),
        ),
      ),
    [projectedPaths, customColumnsTyped?.customColumns],
  );

  const rowScan = useMemo(() => {
    if (isScanSummary) {
      const { customColumns: customColumnFilters } = makeFilterClauses(
        search.filter ?? [],
        customColumnDefinitions,
      );

      return constructFullSummaryScan({
        experimentScanRaw: rowScanRaw,
        summaryScan: initialScan,
        customColumns: customColumnsTyped?.customColumns?.map(({ sql }) => sql),
        customColumnFilters,
      });
    } else {
      const where = buildDefaultWhere(
        {} /* ignore search */,
        fullTextSearchPaths,
      );
      const orderBy = buildDefaultOrderBy(search, undefined, undefined, {
        columns: customColumnDefinitions,
        loaded: customColumnsTyped?.customColumns !== undefined,
      });
      return (
        initialScan &&
        `
        SELECT ${["*"].concat((customColumnsTyped?.customColumns || []).map(({ sql }) => sql)).join(", ")}
        FROM (${initialScan}) "t"
        WHERE ${where}
        ORDER BY ${orderBy}
        `
      );
    }
  }, [
    isScanSummary,
    search,
    fullTextSearchPaths,
    rowScanRaw,
    initialScan,
    customColumnDefinitions,
    customColumnsTyped?.customColumns,
  ]);

  const { data: rowScanNumTracesResult, loadedTag: rowScanNumTracesTag } =
    useDBQuery(
      rowScan &&
        `SELECT COUNT(*) c
        FROM (${rowScan})
      `,
      rowRefreshed,
      {
        loadingTag: rowSearchesHash,
      },
    );
  const rowScanNumTraces = useMemo(
    () =>
      z.coerce
        .number()
        .optional()
        .parse(rowScanNumTracesResult?.toArray()[0]?.toJSON().c),
    [rowScanNumTracesResult],
  );

  const loadingStatus = useMemo((): LoadingStatusType => {
    // Don't make any updates until all of our queries have returned results
    // corresponding to the latest state of all the searches.
    if (
      !(
        rowAllSearchesLoaded &&
        rowPageSearchesHash &&
        rowSearchesHash &&
        lastRowSearchLengthTag === rowPageSearchesHash &&
        rowPaginationStateTag === rowPageSearchesHash &&
        rowScanNumTracesTag === rowSearchesHash
      )
    ) {
      return "pending";
    }
    if (lastRowSearchLength === 0) {
      return "reached_end";
    }
    // This is a backwards-compatibility hack to make sure the object viewer
    // works on old version of the backend. The old backend has a bug where
    // after filtering for old root spans before a certain transaction id, it
    // would return the newest version of those spans, ignoring the transaction
    // id part of the cursor. The result is that the returned page would not
    // actually occur further back in time, and we would keep scrolling forever.
    // To address this, we signal reaching the end if the last two pages have
    // the same pagination state.
    if (
      pages.length > 2 &&
      jsonSerializableDeepEqual(
        pages[pages.length - 2],
        pages[pages.length - 1],
      )
    ) {
      return "reached_end";
    }

    if (rowPaginationState === undefined) {
      throw new Error("Impossible");
    }
    if (rowScanNumTraces !== undefined && rowScanNumTraces < desiredNumTraces) {
      return "desired_num_traces_unmet";
    }
    return "done_loading";
  }, [
    desiredNumTraces,
    lastRowSearchLength,
    lastRowSearchLengthTag,
    pages,
    rowAllSearchesLoaded,
    rowPaginationState,
    rowPaginationStateTag,
    rowScanNumTraces,
    rowScanNumTracesTag,
    rowSearchesHash,
    rowPageSearchesHash,
  ]);

  // Keep track of whether or not each search got us to a "done_loading" state.
  const rowSearchesReachedDoneLoading = useRef<Record<string, boolean>>({});
  useEffect(() => {
    if (rowSearchesHash) {
      rowSearchesReachedDoneLoading.current[rowSearchesHash] =
        rowSearchesReachedDoneLoading.current[rowSearchesHash] ||
        loadingStatus === "done_loading";
    }
  }, [loadingStatus, rowSearchesHash]);

  useEffect(() => {
    if (["desired_num_traces_unmet"].includes(loadingStatus)) {
      if (!(rowPaginationState && rowSearchesHash)) {
        throw new Error("Impossible");
      }
      setPages((pages) => {
        // If we have never reached a "done_loading" status before on this
        // search, it means it hadn't loaded enough rows, so double the fetch
        // limit for the next page.
        const limit = ((): number | undefined => {
          const hasReachedDoneLoading =
            rowSearchesReachedDoneLoading.current[rowSearchesHash];
          if (!hasReachedDoneLoading) {
            return Math.min(
              (pages[pages.length - 1].limit ?? pageSize) * 2,
              MaxLimit,
            );
          } else {
            return undefined;
          }
        })();
        return [
          ...pages,
          { ...rowPaginationState, cursor: lastRowSearchCursor, limit },
        ];
      });
    }
  }, [
    lastRowSearchCursor,
    loadingStatus,
    pageSize,
    rowPaginationState,
    rowSearchesHash,
  ]);

  useObjectIdResolver(rowScanRaw, rowRefreshed);

  useEffect(() => {
    const addRowId = (newId: string | null) =>
      newId &&
      setUrlRowIdsToFetch((rowIds) => {
        if (rowIds.includes(newId)) {
          return rowIds;
        }
        return rowIds.concat([newId]);
      });
    addRowId(makeRowIdPrimary(activeRowId));
    addRowId(navToObjectId);
    // This relies on urlRowId from query params being present in the
    // first render pass, and we only want this effect to run once on page load
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const dml = useMutableObject({
    scan: rowScanRaw,
    objectType: objectType,
    objectId: objectId ?? undefined,
    channel: rowChannelCallback,
    setSavingState,
    auditLogChannel: auditLogChannelCallback,
  });

  const updateRow = useCallback(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async (row: any, path: string[], newValue: any) =>
      await dml.update([row], [{ path, newValue }]),
    [dml],
  );
  const batchUpdateRow = useCallback(
    async (
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      row: any,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      updates: { path: string[]; newValue: any }[],
    ) => await dml.update([row], updates),
    [dml],
  );

  const expandedRowParams: ExpandedRowParams = useMemo(
    () => ({
      primaryScan: rowScanRaw,
      primaryScanReady: rowRefreshed,
      primaryScanChannel: rowChannel,
      primaryDynamicObjectId: objectId,
      comparisonScan: null,
      comparisonScanReady: [],
      auditLogScan: auditLogScanRaw,
      auditLogScanReady: auditLogRefreshed,
      customColumnsParams: {
        scan: rowScan,
        customColumns: customColumnsTyped?.customColumns,
      },
      modelSpecScan,
    }),
    [
      rowScanRaw,
      rowRefreshed,
      rowChannel,
      objectId,
      auditLogScanRaw,
      auditLogRefreshed,
      rowScan,
      customColumnsTyped?.customColumns,
      modelSpecScan,
    ],
  );

  const { hasTags } = useHasTags({
    schema: rowSchema,
  });

  const baseQuery = useMemo(() => {
    const sorts: string[] = [];
    if (search.sort) {
      sorts.push(...search.sort.map((s) => s.text));
    }
    if (!sorts.length) {
      sorts.push(`${CreatedField} DESC, ${ident("id")} DESC`);
    }

    return (
      rowScan &&
      `
        SELECT ${projectedWithCustom.join(", ")}
        FROM (${rowScan}) "t"
        ORDER BY ${sorts.join(", ")}
    `
    );
  }, [search.sort, rowScan, projectedWithCustom]);

  const {
    selectComponent: groupSelectComponent,
    tableGrouping,
    groupAggregationTypes,
    setGroupAggregationType,
  } = useTableGroupingControl({
    baseQuery: disableGrouping ? null : baseQuery,
    signals: rowRefreshed,
    tableIdentifier: objectType + "-" + objectId,
    customFieldNames:
      customColumnsTyped?.customColumns?.map(({ name }) => name) || [],
    viewProps,
  });

  const groupColumnsFn = useCallback(
    (groupColumnName: string) =>
      projectedWithCustom
        .map((f: string) => {
          if (f === groupColumnName) {
            return `COALESCE(${BT_GROUP_KEY}, NULL) AS ${groupColumnName}`;
          }
          if (f === "scores") {
            return `struct_pack(${scoreFields
              .map(
                (s) =>
                  `${doubleQuote(s)} := ${
                    groupAggregationTypes[s] ?? "AVG"
                  }(scores.${doubleQuote(s)})`,
              )
              .join(",\n")}) AS scores`;
          }
          if (f === "metrics") {
            return `struct_pack(${ComputedMetricFields.map(
              (m) =>
                `${doubleQuote(m)} := ${
                  groupAggregationTypes[m] ?? "SUM"
                }(metrics.${doubleQuote(m)})`,
            ).join(",\n")}) AS metrics`;
          }
          if (f === "created") {
            return "MAX(created) AS created";
          }
          return nullValueExpr(f);
        })
        .join(",\n"),
    [groupAggregationTypes, projectedWithCustom, scoreFields],
  );
  const groupQuery = rowGroupingQuery({
    tableGrouping,
    baseQuery: disableGrouping ? null : baseQuery,
    groupColumnName: isScanSummary ? "span_type_info" : "input",
    columnsFn: groupColumnsFn,
    sortExprsFn: (defaultSort) => {
      const orderBy = search?.sort?.length
        ? `${buildDefaultOrderBy(search)},`
        : "";
      return `${orderBy} ${defaultSort}, ${buildDefaultOrderBy({})}`;
    },
  });

  const rowGroupingData = useMemo(() => {
    if (tableGrouping === GROUP_BY_NONE_VALUE) {
      return null;
    }
    return {
      groupRows: [],
      groupBy: tableGrouping,
    };
  }, [tableGrouping]);

  const onTagClick = useMemo(
    () => setTagSearchFn(clauseChecker, viewProps.setSearch),
    [clauseChecker, viewProps.setSearch],
  );

  const tagsFormatter = useTagsFormatter({
    tagConfig: projectConfig.tags,
    onTagClick,
  });

  const tableQuery = useMemo(() => {
    const isGrouping = tableGrouping !== GROUP_BY_NONE_VALUE;
    const groupAggregationProps = {
      groupAggregationTypes,
      setGroupAggregationType,
      isGrouping,
    };
    const pinnedColumnIndex = [
      [
        [{}, {}],
        [
          {
            input: 1,
          },
          {
            input: 2,
          },
        ],
      ],
      [
        [{}, {}],
        [
          {
            span_type_info: 1,
          },
          {
            span_type_info: 2,
          },
        ],
      ],
      // scan summary includes span_type_info
    ][Number(!!isScanSummary)][Number(!!isGrouping)][
      Number(!!enableStarColumn)
    ];
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- copy from other places for now
    const formatters = makeFormatterMap({
      span_type_info: {
        ...GroupKeyFormatterWithCell({
          Formatter: SpanTypeInfoFormatter,
          groupKey: "span_type_info",
        }),
        pinnedColumnIndex: pinnedColumnIndex.span_type_info,
      },
      input: isScanSummary
        ? {}
        : {
            ...GroupKeyFormatterWithCell({
              Formatter: InputFormatter,
              groupKey: "input",
            }),
            pinnedColumnIndex: pinnedColumnIndex.input,
          },
      scores: PercentWithAggregationFormatter(groupAggregationProps),
      ...Object.fromEntries(
        ComputedDurationMetricFields.map((f) => [
          f,
          DurationWithAggregationFormatter(groupAggregationProps),
        ]),
      ),
      ...Object.fromEntries(
        ComputedTokenMetricFields.map((f) => [
          f,
          DefaultWithAggregationFormatter(groupAggregationProps),
        ]),
      ),
      ...Object.fromEntries(
        ComputedCostMetricFields.map((f) => [
          f,
          CostWithAggregationFormatter(groupAggregationProps),
        ]),
      ),
      tags: {
        cell: tagsFormatter,
      },
      start: {
        cell: StartEndFormatter,
      },
      end: {
        cell: StartEndFormatter,
      },
      error: ErrorCellWithFormatter,
      ...Object.fromEntries(
        (customColumnDefinitions || []).map(({ name }) => [
          name,
          { headerLabel: name, headerIcon: CurlyBraces },
        ]),
      ),
    } as FormatterMap<TsTable, TsValue>);

    return {
      type: "tableQuery" as const,
      query: groupQuery ?? baseQuery,
      signals: rowRefreshed,
      formatters,
      rowGroupingData,
    };
  }, [
    baseQuery,
    groupQuery,
    rowRefreshed,
    tagsFormatter,
    isScanSummary,
    groupAggregationTypes,
    setGroupAggregationType,
    tableGrouping,
    rowGroupingData,
    customColumnDefinitions,
    enableStarColumn,
  ]);

  const selectionProps = useTableSelection();

  const traceViewParams = useMemo(
    (): TraceViewParams => ({
      title: objectType,
      objectType: objectType,
      objectId: objectId ?? undefined,
      objectName: objectName ?? "",
      expandedRowParams,
      editableFields: ["expected"],
      updateRow,
      batchUpdateRow,
      commentFn: dml.commentOn,
      deleteCommentFn: dml.deleteComment,
      roster: rowChannelRoster,
      ...traceViewParamArgs,
    }),
    [
      objectType,
      objectId,
      objectName,
      expandedRowParams,
      updateRow,
      batchUpdateRow,
      dml.commentOn,
      dml.deleteComment,
      rowChannelRoster,
      traceViewParamArgs,
    ],
  );

  if (DebugMode) {
    const state = {
      desiredNumTraces,
      pages,
      urlRowIdsToFetch,
      rowPageSearches,
      urlRowSearches,
      rowSearches,
      rowPageSearchesHash,
      rowSearchesHash,
      auditLogSearches,
      rowFirstSearchKey,
      rowChannelSpec,
      rowDataObjects,
      rowDataRefreshed,
      rowDataError,
      rowFnames,
      rowSchemas,
      rowRefresheds,
      rowFilenameToChannel,
      rowSchema,
      rowRefreshed,
      rowChannel,
      rowScanRaw,
      auditLogChannelSpec,
      auditLogDataObjects,
      auditLogDataRefreshed,
      auditLogFnames,
      auditLogSchemas,
      auditLogRefresheds,
      auditLogFilenameToChannel,
      auditLogSchema,
      auditLogRefreshed,
      auditLogChannel,
      auditLogScanRaw,
      rowAllSearchesLoaded,
      lastRowSearchKey,
      lastRowSearchLengthResult,
      lastRowSearchLengthTag,
      lastRowSearchLength,
      rowPageFnames,
      rowPaginationStateResult,
      rowPaginationStateTag,
      rowPaginationState,
      rowScan,
      rowScanNumTracesResult,
      rowScanNumTracesTag,
      rowScanNumTraces,
      loadingStatus,
      rowSearchesReachedDoneLoading,
      selectedBucket,
      tableQuery,
    };
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    (window as any).paginatedObjectViewerDataComponentsState = state;
  }

  return {
    objectType,
    objectId,
    objectName,
    scoreFields,
    pageSize,
    clauseChecker,
    viewProps,
    viewParams,
    pageIdentifier,
    search,
    setSearch: viewProps.setSearch,
    hasTags,
    selectionProps,
    dml,
    loadingStatus,
    urlRowSearches,
    rowSchema,
    rowScanNumTraces,
    rowChannel,
    projectedPaths,
    tableQuery,
    groupSelectComponent,
    traceViewParams,
    setDesiredNumTraces,
    rowDataError,
    selectedBucket,
    customColumns: useMemo(
      () => ({
        options: {
          input: inputFields,
          output: outputFields,
          expected: expectedFields,
          metadata: metadataFields,
        },
        isEnabled: customColumnsEnabled,
        columns: customColumnDefinitions,
        createColumn: createCustomColumn,
        deleteColumn: deleteCustomColumn,
        updateColumn: updateCustomColumn,
        loading: customColumnsTyped?.customColumns === undefined,
        error: customParseColumnsError,
      }),
      [
        createCustomColumn,
        customColumnsEnabled,
        customColumnDefinitions,
        customColumnsTyped?.customColumns,
        customParseColumnsError,
        deleteCustomColumn,
        expectedFields,
        inputFields,
        metadataFields,
        outputFields,
        updateCustomColumn,
      ],
    ),
    filters,
    // This is only supported in summary optimization mode
    newRows: 0,
    onRefreshLiveRows: undefined,
  };
}

export type PaginatedObjectViewerVizComponentsArgs<
  TsTable extends RowData,
  TsValue,
> = {
  rowIds: RowId[];
  setRowIds: Dispatch<SetStateAction<RowId[]>>;
  aiSearchType: AISearchType;
  includeExperimentSelectionSection?: boolean;
  extraLeftControls?: React.ReactNode;
  extraRightControls?: React.ReactNode;
  vizQueryPropsOverride?: Partial<VizQueryProps<TsTable, TsValue>>;
  hideLiveButton?: boolean;
  customError?: React.ReactNode;
};

export type PaginatedObjectViewerVizComponents =
  | { status: "loading_initial" }
  | {
      status: "loaded_table" | "loaded_empty";
      tableViewComponents: React.ReactNode;
      tracePanelComponents: React.ReactNode | undefined;
    };

export const INITIALLY_VISIBLE_COLUMNS = { id: false };

export type UnionedPaginatedObjectViewerDataComponents<
  TsTable extends RowData,
  TsValue,
> = Omit<
  PaginatedObjectViewerDataComponents<TsTable, TsValue>,
  "tableQuery"
> & {
  tableQuery:
    | PaginatedObjectViewerTableQuery<TsTable, TsValue>
    | SummaryPaginatedObjectViewerTableQuery<TsTable, TsValue>;
};

export function usePaginatedObjectViewerVizComponents<
  TsTable extends RowData,
  TsValue,
>(
  {
    objectType,
    objectName,
    scoreFields,
    clauseChecker,
    viewProps,
    viewParams,
    pageIdentifier,
    search,
    setSearch,
    hasTags,
    selectionProps,
    dml,
    loadingStatus,
    urlRowSearches,
    rowScanNumTraces,
    rowChannel,
    projectedPaths,
    tableQuery,
    traceViewParams,
    setDesiredNumTraces,
    rowDataError,
    pageSize,
    selectedBucket,
    objectId,
    customColumns,
    refetchDataQueryFn,
    onRefreshLiveRows,
    newRows,
  }: UnionedPaginatedObjectViewerDataComponents<TsTable, TsValue>,
  {
    rowIds,
    setRowIds,
    aiSearchType,
    includeExperimentSelectionSection,
    extraLeftControls: externalLeftControls,
    extraRightControls: externalRightControls,
    vizQueryPropsOverride,
    hideLiveButton,
    customError,
  }: PaginatedObjectViewerVizComponentsArgs<TsTable, TsValue>,
): PaginatedObjectViewerVizComponents {
  const { selectedRows, setSelectedRows, tableRef } = selectionProps;

  const org = useOrg();
  const [{ r: activeRowId }] = useActiveRowAndSpan();
  const { projectId, projectName } = useContext(ProjectContext);

  const advancedSearchState = useAdvancedSearchState({ setSearch });

  const runAISearchWrapper = useEvent(
    (openAIOpts: ClientOptions, query: string) =>
      runAISearch({
        openAIOpts,
        apiUrl: org.api_url,
        query,
        orgName: org.name,
        searchType: aiSearchType,
        aiSchemaColumns: projectedPaths,
        scoreFields,
      }),
  );

  const extraLeftControls = useMemo(
    () => (
      <>
        <Views
          pageIdentifier={pageIdentifier}
          viewParams={viewParams}
          viewProps={viewProps}
        />
        {externalLeftControls}
      </>
    ),
    [externalLeftControls, pageIdentifier, viewParams, viewProps],
  );

  const [isLive, setIsLive] = useIsLiveState(!!hideLiveButton);

  const extraRightControls = useMemo(
    () => (
      <>
        {onRefreshLiveRows && (
          <LiveButton
            isLive={isLive || viewProps.timeRangeFilter === "1h"}
            setIsLive={setIsLive}
            newRows={newRows}
            onRefresh={onRefreshLiveRows}
            hideLiveButton={true}
          />
        )}
        <AdvancedSearchComponents
          {...advancedSearchState}
          objectType={objectType}
        />
        {externalRightControls}
      </>
    ),
    [
      advancedSearchState,
      externalRightControls,
      objectType,
      isLive,
      setIsLive,
      newRows,
      onRefreshLiveRows,
      viewProps.timeRangeFilter,
    ],
  );

  const showInfiniteScroll =
    loadingStatus !== "reached_end" && !advancedSearchState.disableScroll;

  const infiniteScroll = useMemo(
    () =>
      showInfiniteScroll
        ? async () => {
            if (loadingStatus === "pending") return;
            setDesiredNumTraces((desiredNumTraces) => {
              const latestNumRows = Math.max(
                desiredNumTraces,
                rowScanNumTraces ?? 0,
              );
              return latestNumRows + pageSize;
            });
            // Once the new page is "installed", we'll resolve this promise.
            await new Promise<void>((resolve) => {
              scrollRef.current = resolve;
            });
          }
        : undefined,
    [
      loadingStatus,
      pageSize,
      rowScanNumTraces,
      setDesiredNumTraces,
      showInfiniteScroll,
    ],
  );

  // This ref is used to resolve a pending infinite scroll when we have reached
  // a terminating state.
  const scrollRef = useRef<(() => void) | undefined>(undefined);
  (() => {
    // eslint-disable-next-line react-compiler/react-compiler
    const scrollRefCurrent = scrollRef.current;
    if (
      !(
        scrollRefCurrent &&
        ["reached_end", "done_loading"].includes(loadingStatus)
      )
    ) {
      return;
    }
    scrollRefCurrent();
    // eslint-disable-next-line react-compiler/react-compiler
    scrollRef.current = undefined;
  })();

  // This has to be a ref, because it tells us whether we have _ever_ loaded any traces.
  // If we have, then we never want to show the initial loading state (i.e. loading_initial).
  // In other words, once this is flipped to `true`, we never want to flip it back.
  const hasLoadedTraces = useRef<boolean>(false);
  useEffect(() => {
    if (rowScanNumTraces !== undefined && rowScanNumTraces > 0) {
      hasLoadedTraces.current = true;
    }
  }, [rowScanNumTraces]);

  const { applySearch } = useFilterSortBarSearch({
    runAISearch: runAISearchWrapper,
    clauseChecker,
    setSearch,
  });

  const neverVisibleColumns = useMemo(() => {
    return new Set([TransactionIdField, "scores", "cached", "span_id"]);
  }, []);

  const errorMessage = useMemo(() => {
    return rowDataError ? (
      rowDataError.message.includes("ForbiddenError") ? (
        <>{rowDataError.message}</>
      ) : rowDataError.message.includes("Match search") ? (
        <>
          Search queries are not supported pre-Brainstore. Either enable
          Brainstore for this project or wait for it to backfill to use the
          search box.
        </>
      ) : (
        <>
          Failed to retrieve records from the backend. Your query filter may be
          unsupported. Reach out to{" "}
          <a href="mailto:<EMAIL>" className="underline">
            <EMAIL>
          </a>{" "}
          for help.
          <br />
          Full error: {rowDataError.message}
        </>
      )
    ) : (
      customError
    );
  }, [rowDataError, customError]);

  const queriedRowIds = useMemo(() => {
    return tableQuery.type === "tableData"
      ? tableQuery.data?.map(({ id }) => id)
      : undefined;
  }, [tableQuery]);

  const afterToolbarSlot = useMemo(() => {
    // const liveBanner =
    //   // do not show the banner if live or if a row has been manually added
    //   !onRefreshLiveRows || isLive ? null : (
    //     <NewRowsBanner
    //       newRows={newRows}
    //       onRefresh={onRefreshLiveRows}
    //       traces={objectType !== "dataset"}
    //     />
    //   );

    const genericNewRowsBanner = rowChannel?.insertRecordsError ===
      InsertRecordsResult.FAILURE_EXCEEDS_MAX_BYTES && (
      <div className="sticky left-0 w-full">
        <InfoBanner>
          New rows are available.{" "}
          <a
            className="cursor-pointer font-medium"
            onClick={(e) => {
              e.preventDefault();
              window.location.reload();
            }}
          >
            Refresh the page
          </a>{" "}
          to see the latest rows.
        </InfoBanner>
      </div>
    );

    return (
      <>
        <AppliedFilters
          className="sticky left-0 w-full"
          search={search}
          clauseChecker={clauseChecker}
          setSearch={setSearch}
          runAISearch={runAISearchWrapper}
          fromClause={`${objectType}(${singleQuote(projectId ?? "PROJECT_ID")})`}
        />
        {genericNewRowsBanner}
        {/* {liveBanner} */}
      </>
    );
  }, [
    objectType,
    clauseChecker,
    search,
    setSearch,
    runAISearchWrapper,
    projectId,
    rowChannel?.insertRecordsError,
  ]);

  const defaultToolbarSlot = useMemo(() => {
    return Boolean(
      includeExperimentSelectionSection &&
        projectId &&
        projectName &&
        selectionProps.selectedRowsNumber > 0,
    ) ? (
      <ExperimentSelectionSection
        selectionProps={selectionProps}
        orgId={org.id}
        orgName={org.name}
        objectType={objectType}
        dml={dml}
        experimentId={objectId ?? undefined}
        exportName={objectName ?? ""}
        refetchDataQueryFn={refetchDataQueryFn}
      />
    ) : undefined;
  }, [
    includeExperimentSelectionSection,
    projectId,
    projectName,
    objectId,
    objectName,
    objectType,
    dml,
    org,
    selectionProps,
    refetchDataQueryFn,
  ]);

  const firstRow = useBtql({
    name: "First row check",
    query: useMemo(
      () =>
        objectId
          ? {
              select: [{ alias: "id", expr: Query.ident("id") }],
              from: Query.from(objectType, [objectId], "spans"),
              limit: 1,
            }
          : null,
      [objectId, objectType],
    ),
    disableLimit: true,
    brainstoreRealtime: true,
  });

  const hasNoRows = useMemo(() => {
    return firstRow.data?.toArray().length === 0;
  }, [firstRow.data]);

  if (!hasLoadedTraces && !rowDataError && loadingStatus !== "reached_end") {
    return { status: "loading_initial" };
  }

  const status =
    loadingStatus === "reached_end" && hasNoRows
      ? "loaded_empty"
      : "loaded_table";

  const tableViewComponents = (
    <>
      {tableQuery.type === "tableQuery" ? (
        <VizQuery
          {...tableQuery}
          columnReorderer={undefined}
          customColumns={customColumns}
          objectId={objectId}
          objectType={objectType}
          viewProps={viewProps}
          afterToolbarSlot={afterToolbarSlot}
          typeHints={DuckDBTypeHints[objectType]}
          initiallyVisibleColumns={INITIALLY_VISIBLE_COLUMNS}
          neverVisibleColumns={neverVisibleColumns}
          rowSelection={selectedRows}
          setRowSelection={setSelectedRows}
          disableLimit
          runAISearch={runAISearchWrapper}
          tableRef={tableRef}
          extraLeftControls={extraLeftControls}
          extraRightControls={extraRightControls}
          infiniteScroll={infiniteScroll}
          error={errorMessage}
          rowIds={rowIds}
          setRowIds={setRowIds}
          // Once https://github.com/braintrustdata/braintrust/pull/4363 lands, we should be able
          // to remove this.
          disableNegativeTagFilters={objectType === "project_logs"}
          toolbarSlot={defaultToolbarSlot}
          {...vizQueryPropsOverride}
        />
      ) : (
        <TableUI
          data={tableQuery.data}
          fields={tableQuery.fields}
          // @ts-expect-error TsValue vs unknown type mismatch in formatters
          formatters={tableQuery.formatters}
          customColumns={customColumns}
          objectId={objectId}
          objectType={objectType}
          afterToolbarSlot={afterToolbarSlot}
          viewProps={viewProps}
          initiallyVisibleColumns={INITIALLY_VISIBLE_COLUMNS}
          neverVisibleColumns={neverVisibleColumns}
          rowSelection={selectedRows}
          setRowSelection={setSelectedRows}
          disableLimit
          runAISearch={runAISearchWrapper}
          tableRef={tableRef}
          extraLeftControls={extraLeftControls}
          extraRightControls={extraRightControls}
          infiniteScroll={infiniteScroll}
          error={errorMessage}
          rowIds={rowIds}
          setRowIds={setRowIds}
          queriedRowIds={queriedRowIds}
          // Once https://github.com/braintrustdata/braintrust/pull/4363 lands, we should be able
          // to remove this.
          toolbarSlot={defaultToolbarSlot}
          {...vizQueryPropsOverride}
        />
      )}
    </>
  );

  const tracePanelComponents = activeRowId ? (
    <div className="flex flex-1 flex-col overflow-hidden">
      <TracePanel
        rowIds={rowIds}
        traceViewParams={traceViewParams}
        onApplySearch={applySearch}
      />
    </div>
  ) : undefined;

  return { status, tableViewComponents, tracePanelComponents };
}

export function useFilters({
  search,
  brainstore,
  selectedBucket,
  objectType,
  customColumnDefinitions,
  timeSpan,
}: {
  search: Search;
  brainstore: boolean;
  selectedBucket: Clause<"filter"> | undefined;
  objectType: DataObjectType;
  customColumnDefinitions?: ReturnType<
    typeof useCustomColumns
  >["customColumnDefinitions"];
  timeSpan: ViewProps["timeRangeFilter"];
}) {
  const builder = useBtqlQueryBuilder({});

  return useMemo(() => {
    const colRegexMap = new Map(
      customColumnDefinitions?.map(({ name }) => [
        name,
        new RegExp(`^\`?${name}\`?`),
      ]),
    );
    return {
      sql: (search.filter?.flatMap((f) => f.tree ?? []) || []).concat(
        ...makeTagsFilterTree(search),
      ),
      btql: (search.filter ?? [])
        .reduce((acc: ParsedExpr[], f) => {
          const col = customColumnDefinitions?.find(({ name }) =>
            colRegexMap.get(name)?.test(f.text),
          );
          if (!col) {
            if (f.btql?.parsed) {
              acc.push(f.btql.parsed);
            }
          } else {
            const parser = new Parser(
              f.text.replace(colRegexMap.get(col.name)!, `${col.expr}`),
            );
            acc.push(parser.parseExpr());
          }
          return acc;
        }, [])
        .concat(makeBtqlTagsFilter(search))
        .concat(
          brainstore
            ? makeBrainstoreFullTextFilter(
                builder,
                search,
                [
                  "input",
                  "expected",
                  "metadata",
                  "tags",
                  // A little hacky, but we don't want to include output or span_attributes in the
                  // full text search for datasets.
                ].concat(
                  objectType !== "dataset" ? ["output", "span_attributes"] : [],
                ),
              )
            : makeBtqlFullTextFilter(builder, search, ["input"]),
        )
        .concat(selectedBucket ? [{ btql: selectedBucket.text }] : [])
        .concat(
          objectType !== "project_logs" || !timeSpan || timeSpan === "all"
            ? []
            : typeof timeSpan === "string" && TIME_RANGE_TO_INTERVAL[timeSpan]
              ? [
                  {
                    btql: `created >= NOW() - INTERVAL ${TIME_RANGE_TO_INTERVAL[timeSpan]}`,
                  },
                ]
              : typeof timeSpan === "object"
                ? [
                    {
                      btql: `created >= '${parseDateString(timeSpan.from).toISOString()}' AND created <= '${endOfDay(parseDateString(timeSpan.to)).toISOString()}'`,
                    },
                  ]
                : [],
        ),
    };
  }, [
    customColumnDefinitions,
    search,
    brainstore,
    builder,
    objectType,
    selectedBucket,
    timeSpan,
  ]);
}
