"use client";

import { useArrowAPI } from "#/utils/btapi/btapi";
import { useMaterializedArrow } from "#/utils/duckdb";
import { useMemo } from "react";
import { isEmpty } from "#/utils/object";
import { Field, Float, Int, Precision, Schema, Utf8 } from "apache-arrow";
import { doubleQuote, singleQuote } from "#/utils/sql-utils";
import { type LogTimeBucket } from "#/app/app/[org]/p/[project]/(ExperimentsChart)/logs-progress-insight";
import { toSchemaObject } from "@braintrust/btql/schema";
import { type ResponseSchema } from "@braintrust/btql/binder";
import {
  type DiscriminatedProjectScore,
  isAggregateScore,
  sortScoreFields,
} from "@braintrust/local/query";
import { useBtql } from "#/utils/btql/btql";
import { type ViewProps } from "#/utils/view/use-view";
import {
  type Interval,
  type Expr as ParsedExpr,
} from "@braintrust/btql/parser";
import { type CustomColumn } from "@braintrust/core/typespecs";
import { useIsFeatureEnabled } from "#/lib/feature-flags";
import { replaceFieldWithCustomColumn } from "#/utils/search-btql";
import { useBtqlQueryBuilder } from "#/utils/btql/use-query-builder";

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
const emptyLogSummarySchema = (row: any) => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const scoreFields: Field<any>[] = !isEmpty(row)
    ? Object.keys(row)
        .filter((key) => key.startsWith("avg_"))
        .map((key) =>
          Field.new({ name: key, type: new Float(Precision.DOUBLE) }),
        )
    : [];
  return new Schema(
    [
      Field.new({ name: "bucket", type: new Utf8() }),
      Field.new({ name: "last_updated", type: new Utf8() }),
      Field.new({ name: "num_examples", type: new Int(true, 64) }),
    ].concat(scoreFields),
  );
};

// Note: if the logs do not have any scores, this query will return a completely
// empty result for BTQL and a result with no score columns for the legacy
// endpoint.
export function useLogScoreSummary(
  logBucket: LogTimeBucket,
  projectId: string | null,
  viewProps: ViewProps,
  customColumns?: CustomColumn[],
) {
  const filters = useMemo(() => {
    const ret =
      viewProps?.search?.filter?.reduce((acc: ParsedExpr[], f) => {
        if (f.btql?.parsed) {
          acc.push(replaceFieldWithCustomColumn(f.btql?.parsed, customColumns));
        }
        return acc;
      }, []) ?? [];

    ret.push({
      op: "ge",
      left: {
        op: "ident",
        name: ["created"],
      },
      right: {
        op: "sub",
        left: {
          op: "function",
          name: { op: "ident", name: ["now"] },
          args: [],
        },
        right: logBucketToInterval(logBucket),
      },
    });

    return ret;
  }, [viewProps?.search?.filter, customColumns, logBucket]);

  const enableExpensiveSummaries = useIsFeatureEnabled("enableAdvancedMetrics");
  const projectSummaryMetrics = useIsFeatureEnabled("projectSummaryMetrics");

  const builder = useBtqlQueryBuilder({});

  const btqlSummary = useBtql({
    name: "Score summary query",
    query: useMemo(
      () =>
        projectId && projectSummaryMetrics
          ? {
              from: builder.from("project_logs", [projectId]),
              unpivot: [
                {
                  expr: { btql: "scores" },
                  alias: ["score", "value"],
                },
              ],
              measures: [
                { alias: "last_updated", expr: { btql: "max(created)" } },
                { alias: "num_examples", expr: { btql: "count(1)" } },
                { alias: "avg", expr: { btql: "avg(value)" } },
              ],
              dimensions: [
                {
                  alias: "bucket",
                  expr: {
                    op: "function",
                    name: { op: "ident", name: [logBucket] },
                    args: [{ op: "ident", name: ["created"] }],
                  },
                },
              ],
              pivot: [{ alias: "scores", expr: { btql: "score" } }],
              limit: 10000,
              filter: builder.and(...(filters || [])),
            }
          : null,
      [projectId, projectSummaryMetrics, builder, logBucket, filters],
    ),
    minimumApiVersion: "0.0.50",
    brainstoreRealtime: false,
    expensive: !enableExpensiveSummaries,
  });

  const projectLogParams = useMemo(
    () =>
      projectId && btqlSummary.unsupported
        ? {
            project_id: projectId,
            project_logs: true,
            bucket: logBucket,
          }
        : null,
    [btqlSummary.unsupported, logBucket, projectId],
  );

  const { data: logsSummaryLegacy, loading: legacyLoading } = useArrowAPI({
    name: "Score summary query",
    endpoint: projectLogParams && "summary",
    params: projectLogParams,
    schema: emptyLogSummarySchema,
  });

  const logsSummary = useMemo(
    () =>
      btqlSummary.unsupported ? logsSummaryLegacy : (btqlSummary.data ?? null),
    [btqlSummary.data, btqlSummary.unsupported, logsSummaryLegacy],
  );

  const {
    refreshed: logSummaryReady,
    name: logSummaryTable,
    schema: logSummarySchema,
    hasLoaded: logSummaryTableLoaded,
  } = useMaterializedArrow(`logs_summary_${projectId}`, logsSummary);
  const loading = btqlSummary.unsupported
    ? legacyLoading
    : !logSummaryTableLoaded;

  const { scoreFields, scoreToAlias } = useMemo(
    () =>
      deriveScoreFields({
        summarySchema: logSummarySchema,
        btqlSchema: btqlSummary.schema,
        scoreConfig: [],
      }),
    [btqlSummary.schema, logSummarySchema],
  );

  const logSummaryQuery = useMemo(
    () =>
      logSummaryTable &&
      `
        SELECT
            ${[
              "bucket AS id",
              "last_updated",
              btqlSummary.unsupported || scoreFields.length === 0
                ? "num_examples"
                : `GREATEST(${scoreFields
                    .map(
                      (f) =>
                        `CASE
                          WHEN scores[${singleQuote(f)}] IS NULL THEN 0
                          ELSE scores[${singleQuote(f)}].num_examples
                        END`,
                    )
                    .join(",")}) AS "num_examples"`,
              ...scoreFields.map((f) =>
                btqlSummary.unsupported
                  ? doubleQuote(f)
                  : `CASE
                      WHEN scores[${singleQuote(f)}] IS NULL THEN NULL
                      ELSE scores[${singleQuote(f)}].avg
                    END AS ${doubleQuote(scoreToAlias[f])}`,
              ),
            ].join(",\n")}
        FROM ${doubleQuote(logSummaryTable)}
    `,
    [btqlSummary.unsupported, logSummaryTable, scoreFields, scoreToAlias],
  );
  const logSummarySignals = useMemo(() => [logSummaryReady], [logSummaryReady]);

  return {
    logSummaryQuery,
    logSummarySignals,
    logSummarySchema,
    logSummaryBtqlSchema: btqlSummary.schema,
    logSummaryLoading: loading,
    tooExpensive:
      btqlSummary.error && btqlSummary.error.message.includes("too costly"),
  };
}

export function deriveScoreFields({
  summarySchema,
  btqlSchema,
  scoreConfig,
  existingMapping,
}: {
  summarySchema: Schema | null;
  btqlSchema: ResponseSchema | undefined;
  scoreConfig: DiscriminatedProjectScore[];
  existingMapping?: Record<string, string>;
}): {
  scoreFields: string[];
  scoreAliases: string[];
  scoreToAlias: Record<string, string>;
} {
  if (!summarySchema)
    return { scoreFields: [], scoreAliases: [], scoreToAlias: {} };
  const schemaScoreFields = btqlSchema
    ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      ((toSchemaObject(btqlSchema.items.properties.scores.propertyNames ?? {})
        .enum ?? []) as string[])
    : summarySchema.fields
        .map((f) => f.name.toLowerCase())
        .filter((x) => x.startsWith("avg_"));
  const rawScoreFields = schemaScoreFields.concat(
    (scoreConfig ?? [])
      .filter((s) => isAggregateScore(s.score_type))
      .map((s) => s.name),
  );
  // come up with a unique alias for each field, because DuckDB is case sensitive
  // and will combine columns that are lowercased versions of each other
  const scoreToAlias: Record<string, string> = {};
  const lowercasedCounter: Record<string, number> = {};
  for (const score of rawScoreFields) {
    const scoreName = existingMapping?.[score] ?? score;
    const alias = scoreName.toLowerCase();
    const currentCount = lowercasedCounter[alias] ?? 0;
    scoreToAlias[score] =
      currentCount > 0 ? `${scoreName} (${currentCount})` : scoreName;
    lowercasedCounter[alias] = currentCount + 1;
  }

  const scoreFields = sortScoreFields(
    rawScoreFields,
    scoreConfig,
    (s: string) => s.replace("avg_", ""),
  );
  return {
    scoreFields,
    scoreAliases: scoreFields.map((s) => scoreToAlias[s]),
    scoreToAlias,
  };
}

export const LOG_SUMMARY_BUCKETS = 50;

function logBucketToInterval(logBucket: LogTimeBucket): Interval {
  if (logBucket === "week") {
    return {
      value: LOG_SUMMARY_BUCKETS * 7,
      unit: "day",
      op: "interval",
    };
  } else {
    return {
      value: LOG_SUMMARY_BUCKETS,
      unit: logBucket,
      op: "interval",
    };
  }
}
