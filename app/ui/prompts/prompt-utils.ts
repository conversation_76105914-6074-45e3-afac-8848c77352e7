import {
  REASONING_MODEL_PARAM_SETTINGS,
  PLAYGROUND_DEFAULT_MODEL_PARAMS,
} from "#/app/app/[org]/prompt/[prompt]/model-parameters";
import { isEmpty } from "@braintrust/core";
import {
  type Message,
  type PromptBlockData,
  type MessageRole,
  type ModelParams,
} from "@braintrust/core/typespecs";
import {
  defaultModelParamSettings,
  type ModelFormat,
  modelParamToModelParam,
  type ModelSpec,
} from "@braintrust/proxy/schema";
import { type Draft } from "immer";

type PromptBlockMessageRole = Exclude<MessageRole, "model" | "function">;

export const MESSAGE_ROLES: {
  [name in ModelFormat]: PromptBlockMessageRole[];
} = {
  openai: ["system", "user", "assistant", "tool"],
  anthropic: ["system", "user", "assistant", "tool"],
  google: ["system", "user", "assistant", "tool"],
  js: ["user"],
  window: ["user"],
  converse: ["system", "user", "assistant", "tool"],
};

const COERCED_MESSAGE_ROLES: {
  [messageType in MessageRole]: MessageRole | undefined;
} = {
  system: "system",
  function: undefined,
  tool: "tool",
  user: "user",
  assistant: "assistant",
  model: "assistant",
  developer: "system",
};

export function createPromptMessage(
  role: PromptBlockMessageRole,
  text: string = "",
): Message {
  switch (role) {
    case "tool":
      return { role, content: text, tool_call_id: "" };
    default:
      return { role, content: text };
  }
}

export function setMessageRole({
  message,
  newRole,
  newIsMultimodal,
}: {
  message: Draft<Message>;
  newRole: MessageRole;
  newIsMultimodal: boolean;
}) {
  message.role = newRole;

  if (newRole !== "tool") {
    // @ts-expect-error -- this won't throw an error if missing, and ensures invalid properties are removed
    delete message.tool_call_id;
  }
  if (newRole !== "assistant") {
    // @ts-expect-error -- this won't throw an error if missing, and ensures invalid properties are removed
    delete message.tool_calls;
  }
  if (!newIsMultimodal && Array.isArray(message.content)) {
    const textParts = message.content.filter((part) => part.type === "text");
    if (textParts.length === 1 && textParts[0].type === "text") {
      message.content = textParts[0].text;
    } else {
      message.content = textParts;
    }
  }
}

export function translatePromptModelParams({
  currentModel,
  newModel,
  allAvailableModels,
  params,
}: {
  currentModel?: string;
  newModel: string;
  allAvailableModels: Record<string, ModelSpec>;
  params?: ModelParams;
}) {
  const currentModelSpec = currentModel
    ? allAvailableModels[currentModel]
    : undefined;
  const newModelSpec = allAvailableModels[newModel];

  const currFormat = currentModelSpec?.format;
  const format = newModelSpec?.format;

  // Typescript has a lot of trouble understanding this block of code, because
  // translatedParams is a union of all the possible model params. So we just disable
  // the typesystem and use any... I welcome anyone adventurous enough to take a go at
  // fixing this (while keeping the underlying data structure simple).
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  let translatedParams: any = {
    ...params,
  };

  if (
    (currentModelSpec?.o1_like || currentModelSpec?.reasoning) &&
    !(newModelSpec?.o1_like || newModelSpec?.reasoning)
  ) {
    // NOTE: Keep in sync with PromptEditor.tsx onChangeModel
    const paramsToRemove = Object.keys(REASONING_MODEL_PARAM_SETTINGS).filter(
      (key) => key in translatedParams,
    );
    paramsToRemove.forEach((key) => {
      delete translatedParams[key];
    });
  }

  if (currFormat !== format) {
    translatedParams = { ...PLAYGROUND_DEFAULT_MODEL_PARAMS[format] };
    const formatDefaults = defaultModelParamSettings[format];
    for (const [k, v] of Object.entries(params || {})) {
      // Certain parameters are a huge pain in the butt to recreate, so instead of wiping them
      // out, we just leave them alone and let the proxy deal with them.
      if (k === "response_format") {
        translatedParams[k] = v;
        continue;
      }

      const translatedKey =
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
        modelParamToModelParam[k as keyof ModelParams];
      if (
        !isEmpty(translatedKey) &&
        translatedKey in formatDefaults &&
        !isEmpty(
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          formatDefaults[translatedKey as keyof typeof formatDefaults],
        )
      ) {
        translatedParams[translatedKey] = v;
      }
    }
  }

  return translatedParams;
}

export function convertMessages({
  prompt,
  newModel,
  allAvailableModels,
}: {
  prompt?: Draft<PromptBlockData>;
  newModel: string;
  allAvailableModels: Record<string, ModelSpec>;
}): PromptBlockData {
  const {
    format: newFormat,
    flavor: newFlavor,
    multimodal,
  } = allAvailableModels[newModel] ?? {};
  const firstRole = MESSAGE_ROLES[newFormat][0];
  const newIsMultimodal = multimodal ?? false;

  if (isEmpty(prompt)) {
    if (newFlavor === "completion") {
      return {
        type: "completion",
        content: "",
      };
    }

    return {
      type: "chat",
      messages: [createPromptMessage(firstRole)],
    };
  }

  const currentFlavor = prompt.type;

  if (newFlavor === "completion" && currentFlavor === "chat") {
    const firstMessageContent = prompt.messages[0].content;
    const content = Array.isArray(firstMessageContent)
      ? firstMessageContent
          .map((part) => (part.type === "text" ? part.text : ""))
          .join("\n")
          .trim()
      : (firstMessageContent ?? "");

    return {
      type: "completion",
      content,
    };
  }

  if (newFlavor === "chat" && currentFlavor === "chat") {
    const messages = prompt.messages;

    if (
      messages.length === 1 &&
      messages[0].role === "system" &&
      newFormat !== "openai"
    ) {
      const textContent = Array.isArray(messages[0].content)
        ? messages[0].content
            .map((part) => (part.type === "text" ? part.text : ""))
            .join("\n")
            .trim()
        : messages[0].content;
      // Non-OpenAI formats should not have a single system message
      messages[0] = createPromptMessage("user", textContent);
    } else {
      messages.forEach((message) => {
        const coercedRole = COERCED_MESSAGE_ROLES[message.role];
        const newRole =
          coercedRole &&
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          (MESSAGE_ROLES[newFormat] as MessageRole[]).includes(coercedRole)
            ? coercedRole
            : firstRole;
        setMessageRole({ message, newRole, newIsMultimodal });
      });
    }
    return prompt;
  }

  if (newFlavor === "chat" && currentFlavor === "completion") {
    return {
      type: "chat",
      messages: [createPromptMessage(firstRole, prompt.content)],
    };
  }

  // Fallback for type safety
  return {
    type: "chat",
    messages: [createPromptMessage(firstRole)],
  };
}
