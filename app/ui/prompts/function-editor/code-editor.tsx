import TextEditor, { type TextEditorHandle } from "#/ui/text-editor";
import { useDarkMode } from "#/utils/useDarkMode";
import { javascript } from "@codemirror/lang-javascript";
import { python } from "@codemirror/lang-python";
import { lineNumbers } from "@codemirror/view";
import { githubDark, githubLight } from "@uiw/codemirror-theme-github";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useTsEnv } from "#/ui/prompts/use-ts";
import {
  tsSync,
  tsFacet,
  tsLinter,
  tsAutocomplete,
  tsHover,
  type HoverInfo,
} from "@valtown/codemirror-ts";
import { autocompletion } from "@codemirror/autocomplete";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { Info } from "lucide-react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { cn } from "#/utils/classnames";
import { awesome_line_wrapping_plugin } from "#/ui/codemirror/line-wrapping";

export function CodeEditor({
  savedCode,
  editorRef,
  language,
  readOnly,
  copilotContext,
  onSave,
  onChange,
  className,
  hideBanner,
  hideLineNumbers,
  diffValue,
}: {
  savedCode: string | undefined;
  editorRef?: React.RefObject<TextEditorHandle<string> | null>;
  language: "ts" | "py";
  readOnly?: boolean;
  copilotContext?: CopilotContextBuilder;
  onSave?: VoidFunction;
  onChange?: (code: string) => void;
  className?: string;
  hideBanner?: boolean;
  hideLineNumbers?: boolean;
  diffValue?: string;
}) {
  const { tsEnv } = useTsEnv();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  const [tooltipHighlighter, setTooltipHighlighter] = useState<any>(null);

  useEffect(() => {
    if (language !== "ts") return;
    import("shiki/bundle/web").then(({ getSingletonHighlighter }) => {
      getSingletonHighlighter({
        themes: ["github-light", "github-dark"],
        langs: ["typescript"],
      }).then(setTooltipHighlighter);
    });
  }, [language]);

  const darkMode = useDarkMode();
  const extensions = useMemo(() => {
    const extensionsList = [darkMode ? githubDark : githubLight];
    // This is from https://discuss.codemirror.net/t/making-codemirror-6-respect-indent-for-wrapped-lines/2881/4
    extensionsList.push(...awesome_line_wrapping_plugin);
    if (!hideLineNumbers) {
      extensionsList.push(lineNumbers());
    }
    if (language === "py") {
      extensionsList.push(python());
    }
    if (language === "ts") {
      extensionsList.push(javascript({ typescript: true }));
      if (tsEnv) {
        extensionsList.push(tsFacet.of({ env: tsEnv, path: "scorer.ts" }));
        extensionsList.push(tsSync());
        if (!readOnly) {
          extensionsList.push(tsLinter());
        }
        extensionsList.push(
          autocompletion({
            override: [tsAutocomplete()],
          }),
        );
        extensionsList.push(
          tsHover({
            renderTooltip: (info: HoverInfo) => {
              const dom = document.createElement("div");
              dom.className = "p-2 font-mono text-xs";
              if (info.quickInfo?.displayParts) {
                const code = info.quickInfo.displayParts
                  .map((part) => part.text)
                  .join("");
                const theme = darkMode ? "github-dark" : "github-light";
                const highlightedCode = tooltipHighlighter?.codeToHtml(code, {
                  lang: "typescript",
                  theme,
                  meta: {
                    class: `shiki disable-shiki-dark-override ${theme}`,
                  },
                });
                if (highlightedCode) {
                  dom.innerHTML = highlightedCode;
                } else {
                  dom.textContent = code;
                }
              }
              return { dom };
            },
          }),
        );
      }
    }
    return extensionsList;
  }, [
    darkMode,
    language,
    tsEnv,
    readOnly,
    tooltipHighlighter,
    hideLineNumbers,
  ]);

  const initialCode = useMemo(() => {
    if (savedCode === undefined) {
      return undefined;
    } else {
      return savedCode;
    }
  }, [savedCode]);

  const { flags } = useFeatureFlags();
  const getAutoCompleteContext = useCallback(
    () =>
      copilotContext?.makeCopilotContext({
        type: "function",
        functionType: "function",
      }),
    [copilotContext],
  );

  return (
    <>
      {readOnly && !hideBanner && (
        <div className="flex items-center rounded-md border p-3 text-sm bg-accent-50 border-accent-100 text-primary-700">
          <Info className="mr-1.5 inline-block size-3 flex-none" />
          This function was bundled and uploaded, so you cannot edit it here.
        </div>
      )}
      <TextEditor
        value={initialCode}
        ref={editorRef}
        extensions={extensions}
        className={cn("text-sm", className)}
        styled
        wrap
        readOnly={readOnly}
        placeholder="Enter code"
        getAutoCompleteContext={
          !flags.copilotPrompts ? undefined : getAutoCompleteContext
        }
        onSave={onSave}
        onChange={onChange}
        diffValue={diffValue}
      />
    </>
  );
}
