import { useOrg } from "#/utils/user";
import { type RefObject, useCallback, useEffect, useMemo, useRef } from "react";
import { But<PERSON> } from "#/ui/button";
import { Play } from "lucide-react";
import { type PromptData, type UIFunction } from "#/ui/prompts/schema";
import { isEmpty, updatePathMut } from "#/utils/object";
import { produce } from "immer";
import {
  type DataEditorCopilotContextFn,
  DataTextEditor,
} from "#/ui/data-text-editor";
import { getMustacheVars } from "#/app/app/[org]/prompt/[prompt]/helpers";
import { NoAISecretsEmptyState } from "#/ui/prompts/empty";

import { CollapsibleSection } from "#/ui/collapsible-section";
import { type SetValue, useEntityStorage } from "#/lib/clientDataStorage";
import { Spinner } from "#/ui/icons/spinner";
import { type CopilotContextFormat } from "@braintrust/local/copilot";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import {
  CompletionBlock,
  type CompletionBlockHandle,
} from "#/app/app/[org]/prompt/[prompt]/completion-block";
import { type Message } from "@braintrust/core/typespecs";
import { useDebounce } from "#/utils/useDebouncedCallback";
import { getObjValueByPath } from "@braintrust/core";

const PROMPT_VARIABLES_DEBOUNCE_DELAY = 1000;

export const FunctionRunSection = ({
  showNoConfiguredSecretsMessage,
  copilotContext,
  dataEditorValue,
  setDataEditorValue,
  promptData,
  initialFunction,
  variableData,
  runPrompt,
  ref,
  onAddMessageToPrompt,
}: {
  showNoConfiguredSecretsMessage: boolean;
  copilotContext?: CopilotContextBuilder;
  dataEditorValue: Record<string, unknown>;
  setDataEditorValue: SetValue<Record<string, unknown>>;
  promptData?: PromptData;
  initialFunction: UIFunction | null;
  variableData?: unknown;
  runPrompt: VoidFunction;
  ref: RefObject<CompletionBlockHandle | null>;
  onAddMessageToPrompt?: (message: Message) => void;
}) => {
  const org = useOrg();
  const proxyUrl = org.proxy_url;

  const touchedInputEditorRef = useRef(false);

  const promptVariables = useDebounce(
    useMemo(() => {
      const p = promptData?.prompt;
      if (!p) {
        return undefined;
      }
      const variables = (
        p?.type === "completion"
          ? getMustacheVars(p.content)
          : p?.type === "chat"
            ? p.messages
                .map((m) =>
                  m.content
                    ? typeof m.content === "string"
                      ? m.content
                      : JSON.stringify(m.content)
                    : "",
                )
                .flatMap(getMustacheVars)
            : []
      )
        .map((v) => v[1])
        .filter((v) => v !== "");

      return Array.from(new Set(variables));
    }, [promptData?.prompt]),
    PROMPT_VARIABLES_DEBOUNCE_DELAY,
  );

  const functionSchema = useMemo(
    () =>
      initialFunction?.function_schema?.parameters &&
      typeof initialFunction?.function_schema?.parameters === "object" &&
      "properties" in initialFunction?.function_schema?.parameters
        ? Object.keys(
            initialFunction?.function_schema?.parameters?.properties ?? {},
          )
        : undefined,
    [initialFunction?.function_schema?.parameters],
  );

  const promptVariableUpdates = useMemo(() => {
    if (!isEmpty(promptVariables)) {
      return promptVariables;
    }
    if (!isEmpty(functionSchema)) {
      return functionSchema;
    }
    return null;
  }, [promptVariables, functionSchema]);

  const isMissingPromptVariables = useMemo(() => {
    if (!promptVariableUpdates || promptVariableUpdates.length === 0) {
      return false;
    }

    for (const v of promptVariableUpdates) {
      const path = v.split(".");
      const value = getObjValueByPath(dataEditorValue, path);
      if (value == null) {
        return true;
      }
    }

    return false;
  }, [promptVariableUpdates, dataEditorValue]);

  const addPromptVariables = () => {
    setDataEditorValue(
      produce((currentValue) => {
        // If the user has touched the input editor and set non-empty values, don't auto-remove any variables, but auto-add any new ones.
        // If they haven't, auto-set the variables to whatever has been parsed from the prompt.
        const shouldAppend =
          touchedInputEditorRef.current && hasNonEmptyValues(currentValue);
        const baseValue = shouldAppend ? currentValue : {};
        for (const v of promptVariableUpdates ?? []) {
          updatePathMut(baseValue, v.split("."), "", {
            keepExistingValue: shouldAppend,
          });
        }

        return baseValue;
      }),
    );
  };

  useEffect(() => {
    if (!isEmpty(variableData)) {
      setDataEditorValue((prev) => {
        if (isEmpty(prev) || Object.keys(prev).length === 0) {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
          return variableData as Record<string, unknown>;
        }
        return prev;
      });
    }
  }, [functionSchema, promptVariables, variableData, setDataEditorValue]);

  const makeDataAutocompleteContext: DataEditorCopilotContextFn = useCallback(
    (fmt: CopilotContextFormat) => {
      return copilotContext?.makeCopilotContext({
        type: "row",
        field: "input",
        objectType: null,
        objectName: null,
        row: dataEditorValue ?? {},
        fmt,
      });
    },
    [copilotContext, dataEditorValue],
  );
  const [isCodeExecutionWarmed] = useEntityStorage({
    entityType: "org",
    entityIdentifier: org.id ?? "",
    key: "codeExecutionWarmed",
  });
  // TODO: clarify intent with code execution warming/api url
  const showCodeExecutionWarming = !isCodeExecutionWarmed && !org.api_url;

  return (
    <CollapsibleSection title="Run" keepContentsMounted>
      {showNoConfiguredSecretsMessage ? (
        <NoAISecretsEmptyState orgName={org.name} />
      ) : showCodeExecutionWarming ? (
        <div className="flex items-center gap-1 rounded-md p-3 text-xs bg-primary-50 text-primary-800">
          <Spinner className="size-3" />
          Code execution is initializing. This could take up to 60 seconds.
        </div>
      ) : (
        <>
          <DataTextEditor
            value={dataEditorValue}
            allowedRenderOptions={["yaml", "json"]}
            onChange={(v) => {
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              setDataEditorValue(v as Record<string, unknown>);
            }}
            onBlur={() => {
              touchedInputEditorRef.current = true;
            }}
            formatOnBlur
            makeCopilotContext={makeDataAutocompleteContext}
          />

          <div className="mb-3 mt-2 flex justify-start gap-2">
            <Button
              size="xs"
              onClick={(e) => {
                e.preventDefault();
                runPrompt();
              }}
              Icon={Play}
            >
              Run
            </Button>
            {isMissingPromptVariables && (
              <Button
                size="xs"
                onClick={(e) => {
                  e.preventDefault();
                  addPromptVariables();
                }}
                className="text-primary-500"
                variant="ghost"
              >
                Insert variables from messages
              </Button>
            )}
          </div>
          <CompletionBlock
            ref={ref}
            proxyUrl={proxyUrl}
            onAddMessageToPrompt={onAddMessageToPrompt}
          />
        </>
      )}
    </CollapsibleSection>
  );
};

const hasNonEmptyValues = (obj: Record<string, unknown> | unknown): boolean => {
  if (obj === null || obj === undefined || obj === "") {
    return false;
  }
  if (typeof obj === "object" && !Array.isArray(obj) && obj !== null) {
    return Object.values(obj).some(hasNonEmptyValues);
  }
  return true;
};
