import { cn } from "#/utils/classnames";
import {
  forwardRef,
  type InputHTMLAttributes,
  useImperativeHandle,
  useLayoutEffect,
  useRef,
  useState,
} from "react";

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {}

export const inputClassName =
  "flex h-10 w-full rounded-md border border-primary-200 bg-primary-50 dark:bg-primary-100 px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground placeholder:opacity-75 focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 tabular-nums [-moz-appearance:textfield] [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none";

export const Input = forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(inputClassName, className)}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = "Input";

type AutosizeInputProps = {
  containerClassName?: string;
} & InputProps;

export const AutosizeInput = forwardRef<HTMLInputElement, AutosizeInputProps>(
  ({ containerClassName, ...props }: AutosizeInputProps, ref) => {
    const { onChange } = props;
    const inputRef = useRef<HTMLInputElement | null>(null);
    useImperativeHandle(ref, () => inputRef.current!, []);
    // to calculate the width of the input to make the input box size properly
    // https://stackoverflow.com/a/65024003
    const inputWidthSpanRef = useRef<HTMLSpanElement>(null);
    const [inputWidth, setInputWidth] = useState<string>();
    const [inputText, setInputText] = useState(props.defaultValue ?? "");
    const [measureSpanFontSize, setMeasureSpanFontSize] = useState<string>();

    useLayoutEffect(() => {
      const computedStyle = inputRef?.current
        ? window.getComputedStyle(inputRef.current)
        : null;
      const spacing = [
        computedStyle?.paddingLeft ?? 0,
        computedStyle?.paddingRight ?? 0,
        computedStyle?.borderLeftWidth ?? 0,
        computedStyle?.borderRightWidth ?? 0,
      ].join(" + ");
      setInputWidth(
        inputWidthSpanRef.current?.offsetWidth
          ? `calc(${inputWidthSpanRef.current?.offsetWidth}px + ${spacing})`
          : undefined,
      );
    }, [inputText, measureSpanFontSize]);

    return (
      <div
        className={cn("flex flex-col", containerClassName)}
        style={{
          width: inputWidth,
        }}
      >
        <Input
          {...props}
          ref={(node) => {
            inputRef.current = node ?? null;
            if (node) {
              setMeasureSpanFontSize(window.getComputedStyle(node)?.fontSize);
            }
          }}
          onChange={(e) => {
            setInputText(e.target.value);
            if (onChange) {
              onChange(e);
            }
          }}
        />
        <span
          className={cn("invisible h-0 self-start whitespace-pre")}
          style={{
            fontSize: measureSpanFontSize,
          }}
          ref={inputWidthSpanRef}
        >
          {inputText}
        </span>
      </div>
    );
  },
);
AutosizeInput.displayName = "AutosizeInput";
