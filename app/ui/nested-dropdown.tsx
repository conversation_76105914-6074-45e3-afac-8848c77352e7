import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { Search } from "lucide-react";
import { Input } from "#/ui/input";
import {
  type ForwardRefExoticComponent,
  type HTMLAttributes,
  type PropsWithChildren,
  type RefAttributes,
  useCallback,
  useMemo,
  useRef,
  useState,
} from "react";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";

export type AdditionalAction = {
  label: React.ReactNode;
  className?: string;
  onSelect?: (e: Event) => void;
};

interface NestedDropdownProps<T> {
  open?: boolean;
  setOpen?: (open: boolean) => void;
  isInSubMenu?: boolean;
  objectType: string;
  dropdownItems?: {
    groupLabel: string;
    items: T[];
    hideGroupLabel?: boolean;
  };
  renderItems?: (
    items: T[],
    DropdownItemComponent: ForwardRefExoticComponent<
      { item: T } & RefAttributes<HTMLDivElement>
    >,
    isSearching?: boolean,
  ) => React.ReactNode;
  subGroups?: {
    groupLabel: string;
    items: T[];
  }[];
  renderGroupLabel?: (label: string, nested?: boolean) => React.ReactNode;
  DropdownItemComponent: ForwardRefExoticComponent<
    { item: T } & RefAttributes<HTMLDivElement> & HTMLAttributes<HTMLDivElement>
  >;
  selectedItems?: T | T[];
  filterItems: (search: string, items: T[]) => T[];
  additionalActions?: AdditionalAction[];
  align?: DropdownMenuContentProps["align"];
  hideSelected?: boolean;
  modal?: boolean;
  emptyMessage?: React.ReactNode;
}

export const NestedDropdown = <T,>({
  open,
  setOpen,
  isInSubMenu,
  objectType,
  dropdownItems,
  renderItems,
  subGroups,
  additionalActions,
  filterItems,
  align = "start",
  renderGroupLabel,
  DropdownItemComponent,
  selectedItems,
  hideSelected,
  modal,
  children,
  emptyMessage,
}: PropsWithChildren<NestedDropdownProps<T>>) => {
  const [search, setSearch] = useState("");

  const filtered = useMemo(() => {
    const combined = [...(dropdownItems ? [dropdownItems] : [])].concat(
      subGroups ?? [],
    );
    return search.length
      ? combined.reduce((acc: [string, T[]][], { groupLabel, items }) => {
          const filtered = items && filterItems(search, items);
          if (filtered && filtered.length) {
            return acc.concat([[groupLabel, filtered]]);
          }
          return acc;
        }, [])
      : undefined;
  }, [dropdownItems, subGroups, search, filterItems]);

  const inputRef = useRef<HTMLInputElement>(null);
  const firstItemRef = useRef<HTMLDivElement>(null);
  const lastItemRef = useRef<HTMLDivElement>(null);
  const firstSubMenuRef = useRef<HTMLDivElement>(null);

  const selectedItemsArray = useMemo(() => {
    return selectedItems
      ? Array.isArray(selectedItems)
        ? selectedItems
        : [selectedItems]
      : [];
  }, [selectedItems]);

  const pruneItems = useCallback(
    (items: T[]) => {
      if (search || !selectedItemsArray.length) {
        return items;
      }
      return items.filter((item) => !selectedItemsArray.includes(item));
    },
    [selectedItemsArray, search],
  );

  const contents = (
    <>
      <div className="flex flex-none items-center px-2">
        <Search className="size-3 shrink-0 opacity-50" />
        <Input
          ref={inputRef}
          type="text"
          placeholder={`Find a ${objectType.toLowerCase()}`}
          tabIndex={0}
          onKeyDown={(e) => {
            e.stopPropagation();
            if (e.key === "ArrowDown") {
              e.preventDefault();
              firstItemRef.current?.focus();
              firstSubMenuRef.current?.focus();
            }
          }}
          autoFocus
          className="h-7 border-0 px-1.5 text-xs outline-none ring-0 !bg-transparent focus-visible:border-0 focus-visible:ring-0"
          onChange={(e) => {
            e.stopPropagation();
            e.preventDefault();
            setSearch(e.target.value);
          }}
        />
      </div>
      {!filtered &&
      (!dropdownItems || dropdownItems?.items.length === 0) &&
      (!subGroups ||
        subGroups?.length === 0 ||
        subGroups?.every(({ items }) => items.length === 0)) ? (
        <>
          <DropdownMenuSeparator />
          <span className="flex w-full flex-1 items-center px-2 py-1.5 text-xs text-primary-500">
            {emptyMessage || `No ${objectType}s found`}
          </span>
        </>
      ) : (
        <>
          {!hideSelected && selectedItemsArray.length > 0 && !search && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuGroup className="-m-1 flex-1 overflow-auto p-1">
                {selectedItemsArray.map((item, i) => (
                  <DropdownItemComponent
                    onKeyDown={(e) => {
                      if (i === 0 && e.key === "ArrowUp") {
                        e.preventDefault();
                        inputRef.current?.focus();
                      }
                    }}
                    item={item}
                    key={i}
                    ref={i === 0 ? firstItemRef : undefined}
                  />
                ))}
              </DropdownMenuGroup>
            </>
          )}
          {filtered ? (
            filtered.length ? (
              <>
                <DropdownMenuSeparator />
                <div className="-m-1 flex-1 overflow-auto p-1">
                  {filtered.map(([group, items], i) =>
                    items.length ? (
                      <DropdownMenuGroup key={group}>
                        <DropdownMenuLabel className="pl-2 text-xs font-normal text-primary-500">
                          {renderGroupLabel
                            ? renderGroupLabel(group, true)
                            : group}
                        </DropdownMenuLabel>
                        {items.map((m, j) => (
                          <DropdownItemComponent
                            onKeyDown={(e) => {
                              if (
                                i === 0 &&
                                j === 0 &&
                                (!selectedItems || !!search) &&
                                e.key === "ArrowUp"
                              ) {
                                e.preventDefault();
                                inputRef.current?.focus();
                              }
                            }}
                            item={m}
                            key={j}
                            ref={
                              i === 0 && j === 0 && (!selectedItems || !!search)
                                ? firstItemRef
                                : undefined
                            }
                          />
                        ))}
                      </DropdownMenuGroup>
                    ) : null,
                  )}
                </div>
              </>
            ) : (
              <>
                <DropdownMenuSeparator />
                <span className="flex w-full flex-1 items-center px-2 py-1.5 text-xs text-primary-500">
                  No {objectType}s found
                </span>
              </>
            )
          ) : (
            <>
              {dropdownItems && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup className="overflow-auto">
                    <DropdownMenuLabel>
                      {dropdownItems.groupLabel}
                    </DropdownMenuLabel>
                    {renderItems
                      ? renderItems(
                          dropdownItems.items,
                          DropdownItemComponent,
                          !!search,
                        )
                      : dropdownItems.items.map((item, i) => (
                          <DropdownItemComponent
                            key={i}
                            item={item}
                            ref={
                              !selectedItems || !search
                                ? i === 0
                                  ? firstItemRef
                                  : i === dropdownItems.items.length - 1
                                    ? lastItemRef
                                    : undefined
                                : undefined
                            }
                            onKeyDown={(e) => {
                              if (
                                i === 0 &&
                                (!selectedItems || !search) &&
                                e.key === "ArrowUp"
                              ) {
                                e.preventDefault();
                                inputRef.current?.focus();
                              }
                            }}
                          />
                        ))}
                  </DropdownMenuGroup>
                </>
              )}
              {subGroups && subGroups?.length > 0 && <DropdownMenuSeparator />}
              <DropdownMenuGroup className="overflow-auto">
                {subGroups?.map(({ groupLabel, items }, i) => {
                  const prunedItems = pruneItems(items);
                  if (!prunedItems || prunedItems.length === 0) {
                    return null;
                  }
                  return (
                    <DropdownMenuSub key={i}>
                      <DropdownMenuSubTrigger
                        ref={
                          i === 0 &&
                          !dropdownItems &&
                          (selectedItemsArray.length === 0 || !!search)
                            ? firstSubMenuRef
                            : undefined
                        }
                        onKeyDown={(e) => {
                          if (
                            i === 0 &&
                            (selectedItemsArray.length === 0 || !!search) &&
                            e.key === "ArrowUp"
                          ) {
                            e.preventDefault();
                            if (!dropdownItems?.items.length) {
                              inputRef.current?.focus();
                            } else {
                              lastItemRef.current?.focus();
                            }
                          }
                        }}
                      >
                        {renderGroupLabel
                          ? renderGroupLabel(groupLabel)
                          : groupLabel}
                      </DropdownMenuSubTrigger>
                      <DropdownMenuSubContent className="w-80 overflow-auto">
                        <DropdownMenuGroup>
                          {renderItems
                            ? renderItems(
                                items,
                                DropdownItemComponent,
                                !!search,
                              )
                            : items.map((item, i) => (
                                <DropdownItemComponent key={i} item={item} />
                              ))}
                        </DropdownMenuGroup>
                      </DropdownMenuSubContent>
                    </DropdownMenuSub>
                  );
                })}
              </DropdownMenuGroup>
            </>
          )}
        </>
      )}
      {!!additionalActions && additionalActions.length > 0 && (
        <>
          <DropdownMenuSeparator />
          {additionalActions.map(({ label, onSelect, className }, i) => (
            <DropdownMenuItem
              asChild
              onSelect={onSelect}
              key={i}
              className={className}
            >
              {label}
            </DropdownMenuItem>
          ))}
        </>
      )}
    </>
  );

  if (isInSubMenu) {
    return (
      <DropdownMenuSubContent className="flex w-80 flex-col overflow-auto">
        {contents}
      </DropdownMenuSubContent>
    );
  }

  return (
    <DropdownMenu
      open={open}
      onOpenChange={(open) => {
        !open && setSearch("");
        setOpen?.(open);
      }}
      modal={modal}
    >
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent
        align={align}
        className="flex w-80 flex-col overflow-hidden"
        id={`nested-dropdown-${objectType}`}
      >
        {contents}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
