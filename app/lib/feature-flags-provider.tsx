"use client";

import * as semver from "semver";
import { createContext, useCallback, useEffect, useMemo } from "react";
import {
  featureFlagConfig,
  type FeatureFlags,
  featureFlagsSchema,
  ORGS_WITHOUT_ADVANCED_METRICS,
  ORGS_WITHOUT_PROJECT_SUMMARY,
} from "./feature-flag-config";
import {
  type CreateSourceOptions,
  type FlagOptions,
  type RootArgs,
} from "#/generated/hypertune";
import { HypertuneProvider, useHypertune } from "#/generated/hypertune.react";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { useIsClient } from "#/utils/use-is-client";
import { useOrg, useUser } from "#/utils/user";
import { isEmpty } from "@braintrust/core";
import { useSetAtom } from "jotai";
import { useEntityStorage } from "./clientDataStorage";
import { featuresAtom } from "./feature-flag-atoms";

interface FeatureFlagsContextT {
  flags: Record<keyof FeatureFlags, boolean>;
  forcedFlags: Record<string, boolean>;
  setFlag: (flag: keyof FeatureFlags, enabled: boolean) => void;
  isLoading: boolean;
  isInsideProvider: boolean;
}

const DefaultFeatureFlagsContext = {
  flags: featureFlagsSchema.parse({}),
  forcedFlags: {},
  setFlag: () => {},
  isLoading: true,
  isInsideProvider: false, // so we can detect usage outside of provider
};

export const FeatureFlagsContext = createContext<FeatureFlagsContextT>(
  DefaultFeatureFlagsContext,
);

function FeatureFlagProviderInternal({
  children,
}: {
  children: React.ReactNode;
}) {
  const isClient = useIsClient();
  const [value, setValue] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "",
    key: "featureFlags",
  });

  const hypertune = useHypertune();
  const setAtomFeatures = useSetAtom(featuresAtom);

  const setFlag = useCallback(
    (flag: keyof FeatureFlags, enabled: boolean): void => {
      setValue((flags) => ({ ...flags, [flag]: enabled }));
    },
    [setValue],
  );

  const { version: apiVersion, brainstore_default } = useAPIVersion();
  const org = useOrg();

  const { effectiveFlags, forcedFlags } = useMemo(() => {
    const hypertuneRoot = hypertune.get();
    const forced: Record<string, boolean> = {};
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const flags = Object.fromEntries(
      Object.entries(featureFlagConfig).map(([key, def]) => {
        const shouldCheckMinVersion = def.minVersion !== "0.0.0";
        if (
          shouldCheckMinVersion &&
          (!apiVersion || semver.lt(apiVersion, def.minVersion))
        ) {
          return [key, false];
        } else {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const k = key as keyof FeatureFlags;
          const userSetting = value?.[k];

          // getFieldValue will throw error if missing from schema
          // so first check it exists in root
          let remoteValue: FlagOptions = "configurable";
          if (k in hypertuneRoot) {
            try {
              const flagGet = hypertune.getFieldValue(k, {
                fallback: "configurable",
              });
              if (flagGet === "forceOn" || flagGet === "forceOff") {
                remoteValue = flagGet;
              }
            } catch {
              // ignore get failure
            }
          }

          if (remoteValue === "forceOn") {
            forced[key] = true;
            return [key, true];
          } else if (remoteValue === "forceOff") {
            forced[key] = true;
            return [key, false];
          } else if (!isEmpty(userSetting)) {
            // User override when no remote value
            const parsedValue = featureFlagsSchema.shape[k]?.parse(userSetting);
            return [key, parsedValue];
          } else {
            // Fall back to existing logic for code defaults
            if (
              k === "brainstore" &&
              brainstore_default &&
              isEmpty(userSetting)
            ) {
              return [key, true];
            } else if (
              isEmpty(userSetting) &&
              ((k === "enableAdvancedMetrics" &&
                ORGS_WITHOUT_ADVANCED_METRICS.includes(org.name)) ||
                (k === "projectSummaryMetrics" &&
                  ORGS_WITHOUT_PROJECT_SUMMARY.includes(org.name)))
            ) {
              return [key, false];
            } else if (
              def.minDefaultVersion &&
              semver.gte(apiVersion, def.minDefaultVersion) &&
              isEmpty(userSetting) &&
              brainstore_default
            ) {
              return [key, true];
            }
            const parsedValue = featureFlagsSchema.shape[k]?.parse(undefined);
            return [key, parsedValue];
          }
        }
      }),
    ) as FeatureFlags;
    const checked = new Set<keyof FeatureFlags>();
    for (const flag of Object.keys(flags)) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      disableDependentFlag(flags, flag as keyof FeatureFlags, checked);
    }
    return { effectiveFlags: flags, forcedFlags: forced };
  }, [apiVersion, value, brainstore_default, org.name, hypertune]);

  const effectiveFlagsHash = useMemo(() => {
    return JSON.stringify(effectiveFlags);
  }, [effectiveFlags]);

  const forcedFlagsHash = useMemo(() => {
    return JSON.stringify(forcedFlags);
  }, [forcedFlags]);

  // update the jotai store of feature flags
  useEffect(() => {
    setAtomFeatures(effectiveFlags);
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [effectiveFlagsHash]);

  const ctxValue = useMemo(() => {
    return {
      flags: effectiveFlags,
      forcedFlags,
      setFlag,
      isLoading: !isClient,
      isInsideProvider: true,
    };
    // only update when one of the deep values of flags or forced changes instead of object
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [effectiveFlagsHash, forcedFlagsHash, setFlag, isClient]);

  return (
    <FeatureFlagsContext.Provider value={ctxValue}>
      {children}
    </FeatureFlagsContext.Provider>
  );
}

function disableDependentFlag(
  flags: FeatureFlags,
  flag: keyof FeatureFlags,
  checked: Set<keyof FeatureFlags>,
) {
  const config = featureFlagConfig[flag];
  if (!config || checked.has(flag)) {
    return;
  }
  checked.add(flag);
  if (config.dependsOn) {
    disableDependentFlag(flags, config.dependsOn, checked);
    flags[flag] = flags[flag] && flags[config.dependsOn];
  }
  return;
}

const hypertuneCreateSourceOptions: CreateSourceOptions = {
  token: process.env.NEXT_PUBLIC_HYPERTUNE_TOKEN!,
  initDataRefreshIntervalMs: 30_000,
};

export function FeatureFlagProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useUser();
  const org = useOrg();

  const args: RootArgs = useMemo(() => {
    return {
      context: {
        environment:
          process.env.NODE_ENV === "production" ? "production" : "development",
        user: {
          id: user?.id || "anonymous",
          email: user?.email || "none",
        },
        org: {
          id: org?.id || "none",
          name: org?.name || "none",
        },
      },
    };
  }, [user, org]);

  return (
    <HypertuneProvider
      createSourceOptions={hypertuneCreateSourceOptions}
      rootArgs={args}
    >
      <FeatureFlagProviderInternal>{children}</FeatureFlagProviderInternal>
    </HypertuneProvider>
  );
}
