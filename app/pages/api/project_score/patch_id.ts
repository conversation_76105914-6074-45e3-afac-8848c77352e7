import type { NextApiRequest, NextApiResponse } from "next";
import { runJsonRequest } from "../_request_util";
import {
  idParamSchema,
  extractSingularRow,
  patchObjects,
} from "../_object_crud_util";
import { categoriesParamSchema } from "./util";
import {
  projectScoreSchema,
  patchProjectScoreSchema,
} from "@braintrust/core/typespecs";

const paramsSchema = idParamSchema.merge(patchProjectScoreSchema).extend({
  categories: categoriesParamSchema,
  config: projectScoreSchema.shape.config.nullish(),
  user_id: projectScoreSchema.shape.user_id.nullish(),
});

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ id, categories, ...paramsRest }, authLookup) =>
      extractSingularRow({
        rows: await patchObjects({
          authLookup,
          priorObjectTables: ["project_score"],
          permissionInfo: {
            aclObjectType: "project",
            aclPermission: "update",
          },
          filters: {
            id,
          },
          patchValueParams: paramsRest,
          noCoalescePatchValueParams:
            categories !== undefined ? { categories } : undefined,
          fullResultsSize: 1,
        }),
        notFoundErrorMessage: undefined,
      }),
    {
      paramsSchema,
      outputSchema: projectScoreSchema,
    },
  );
}
