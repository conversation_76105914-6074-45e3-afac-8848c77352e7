const path = require("path");

const isEslintFull = <PERSON><PERSON>an(process.env.ESLINT_FULL);

module.exports = {
  extends: [
    "next/core-web-vitals",
    "next/typescript",
    "plugin:@typescript-eslint/recommended",
    "plugin:tailwindcss/recommended",
    "prettier",
    "plugin:import/typescript",
  ],
  plugins: [
    "@typescript-eslint",
    "@limegrass/import-alias",
    "eslint-plugin-react-compiler",
  ],
  settings: {
    tailwindcss: {
      // https://github.com/francoismassart/eslint-plugin-tailwindcss/issues/241
      config: path.join(__dirname, "./tailwind.config.js"),
    },
    "import/resolver": {
      typescript: true,
      node: true,
    },
  },
  rules: {
    "@next/next/no-img-element": "off",
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        vars: "all",
        args: "none",
        caughtErrors: "none",
        ignoreRestSiblings: false,
        argsIgnorePattern: "^_",
        varsIgnorePattern: "^_",
      },
    ],
    "@typescript-eslint/no-unused-expressions": [
      "error",
      {
        allowShortCircuit: true,
      },
    ],
    "prefer-const": "error",
    "tailwindcss/no-custom-classname": "off",
    "@typescript-eslint/ban-types": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-require-imports": "off",
    // probably should turn this on at some point
    "@typescript-eslint/no-empty-object-type": "off",
    "react-hooks/exhaustive-deps": [
      "error",
      {
        // exact match for useBtql hook
        additionalHooks: "^(useBtql)$",
      },
    ],
    "@limegrass/import-alias/import-alias": [
      "error",
      {
        relativeImportOverrides: [
          { path: "app", depth: 3 },
          { path: "pages", depth: Infinity },
          { path: "/", depth: 0 },
        ],
      },
    ],
    "@typescript-eslint/consistent-type-assertions": [
      "error",
      {
        assertionStyle: "never",
      },
    ],
    "@typescript-eslint/consistent-type-imports": [
      "error",
      {
        fixStyle: "inline-type-imports",
      },
    ],
    "import/no-cycle": isEslintFull ? "error" : "off",
    "no-restricted-imports": [
      "warn",
      {
        paths: [
          {
            name: "@dnd-kit/core",
            importNames: ["DndContext"],
            message:
              "Please use StyledDndContext to enable grabbing cursor styles.",
          },
        ],
      },
    ],
    "react-compiler/react-compiler": "error",
  },
};
