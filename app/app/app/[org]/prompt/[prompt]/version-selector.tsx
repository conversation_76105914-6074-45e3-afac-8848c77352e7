import { Button } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { useRowAuditLog } from "#/ui/trace/query";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { prettifyXact } from "@braintrust/core";
import { TransactionIdField } from "@braintrust/local/query";
import { useOpenedPlaygroundPrompt } from "../../p/[project]/prompts/open";
import { BasicTooltip } from "#/ui/tooltip";
import { PromptPreviewTooltip } from "../../p/[project]/playgrounds/[playground]/prompt-preview-tooltip";
import { cn } from "#/utils/classnames";
import { smartTimeFormat } from "#/ui/date";

type VersionSelectorProps<T> = {
  className?: string;
  label?: string;
  rowId: string | null;
  objectId: string | null;
  objectType: DataObjectType;
  selectedVersion: string;
  onVersionChange: (newObject: T) => void;
  VersionItemRenderer: (props: VersionItemProps<T>) => React.ReactNode;
};

export function VersionSelector<T>({
  className,
  rowId,
  objectId,
  objectType,
  selectedVersion,
  onVersionChange,
  VersionItemRenderer,
  label,
}: VersionSelectorProps<T>) {
  const { auditLogData } = useRowAuditLog({
    auditLogScan: null,
    auditLogReady: [],
    rowId,
    dynamicObjectId: objectId,
    objectType,
  });

  const versions = auditLogData
    .filter(({ audit_data }) => !!audit_data)
    .map((row) => ({
      version: row[TransactionIdField],
      created: row.created,
    }));

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          className={cn(
            "max-w-full truncate rounded-l-none border-l border-primary-200",
            className,
          )}
          size="xs"
          isDropdown
        >
          {label && (
            <span className="flex-1 truncate text-left font-normal text-primary-500">
              {label}
            </span>
          )}
          <span className="flex-none font-mono text-xs text-primary-500">
            {prettifyXact(selectedVersion)}
          </span>
          {versions?.[0]?.version === selectedVersion && (
            <span className="flex-none text-primary-400">(latest)</span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Select version</DropdownMenuLabel>
        {versions?.map((option, idx) => (
          <VersionItemRenderer
            key={option.version}
            isLatest={idx === 0}
            selectedVersion={selectedVersion}
            version={option.version}
            onVersionChange={onVersionChange}
            rowId={rowId}
            date={option.created}
            objectId={objectId}
          />
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

type VersionItemProps<T> = {
  selectedVersion: string;
  version: string;
  onVersionChange: (newObject: T) => void;
  rowId: string | null;
  objectId: string | null;
  date: string;
  isLatest?: boolean;
};

export function PromptVersionItem({
  selectedVersion,
  version,
  onVersionChange,
  rowId,
  objectId,
  date,
  isLatest,
}: VersionItemProps<ReturnType<typeof useOpenedPlaygroundPrompt>>) {
  const prompt = useOpenedPlaygroundPrompt({
    promptId: rowId ?? "",
    projectId: objectId ?? "",
    promptVersion: version,
  });

  return (
    <BasicTooltip
      tooltipContent={
        <PromptPreviewTooltip
          prompt={prompt.prompt?.prompt_data}
          promptMeta={{
            name: prompt.prompt?.name,
            description: prompt.prompt?.description,
            metadata: prompt.prompt?.metadata,
          }}
          version={prompt.prompt?.[TransactionIdField]}
        />
      }
      side="left"
    >
      <DropdownMenuCheckboxItem
        checked={version === selectedVersion}
        onClick={() => {
          onVersionChange(prompt);
        }}
        className="gap-3"
      >
        <span className="flex-1 font-mono text-xs">
          {prettifyXact(version)}
        </span>
        <span className="ml-2 flex-none text-primary-500">
          {isLatest ? "(latest) " : null}
          <time dateTime={new Date(date)?.toISOString()}>
            {smartTimeFormat(new Date(date).getTime())}
          </time>
        </span>
      </DropdownMenuCheckboxItem>
    </BasicTooltip>
  );
}
