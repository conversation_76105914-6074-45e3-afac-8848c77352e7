"use client";

import React, { useContext } from "react";
import { ProjectContext } from "./projectContext";
import {
  Bolt,
  Database,
  MessageCircle,
  Percent,
  Plus,
  Route,
} from "lucide-react";
import Link from "next/link";
import { TileList, TileListItem } from "#/ui/tile-list";
import { smartDateFormat } from "#/ui/date";
import { getProjectLink } from "./getProjectLink";
import { useFunctionsList } from "#/ui/prompts/use-functions-list";
import { ModelOptionLabel } from "../../prompt/[prompt]/model-icon";
import { Skeleton } from "#/ui/skeleton";
import {
  getDatasetLink,
  getDatasetsLink,
} from "./datasets/[dataset]/getDatasetLink";
import { useOrg } from "#/utils/user";
import { useCreateDatasetDialog } from "#/ui/dialogs/create-dataset";
import { useRouter } from "next/navigation";
import { Button, buttonVariants } from "#/ui/button";
import { getSavedPromptLink } from "../../prompt/[prompt]/getPromptLink";
import { cn } from "#/utils/classnames";

export const LibraryTiles = ({
  isProjectLoading,
}: {
  isProjectLoading: boolean;
}) => {
  const { name: orgName, id: orgId } = useOrg();
  const { projectName, mutateDatasets, projectDatasets } =
    useContext(ProjectContext);
  const router = useRouter();

  const { modal: createDatasetModal, open: openCreateDatasetModal } =
    useCreateDatasetDialog({
      orgId,
      projectName: projectName,
      onSuccessfulCreate: ({ datasetName }) => {
        mutateDatasets();
        router.push(
          getDatasetLink({
            orgName,
            projectName,
            datasetName,
          }),
        );
      },
    });

  const {
    loading: promptsLoading,
    data: promptsData,
    error: promptsError,
  } = useFunctionsList({
    functionObjectType: "prompt",
    limit: 3,
  });
  const prompts = promptsData?.toArray();

  const {
    loading: scorersLoading,
    data: scorersData,
    error: scorersError,
  } = useFunctionsList({
    functionObjectType: "scorer",
    limit: 3,
  });
  const scorers = scorersData?.toArray();

  const {
    loading: toolsLoading,
    data: toolsData,
    error: toolsError,
  } = useFunctionsList({
    functionObjectType: "tool",
    limit: 3,
  });
  const tools = toolsData?.toArray();

  const {
    loading: agentsLoading,
    data: agentsData,
    error: agentsError,
  } = useFunctionsList({
    functionObjectType: "agent",
    limit: 3,
  });
  const agents = agentsData?.toArray();

  const datasets = projectDatasets.sort(
    (a, b) =>
      new Date(b.created ?? "").getTime() - new Date(a.created ?? "").getTime(),
  );

  return (
    <div className="flex w-full max-w-md flex-none flex-col gap-8">
      <LibrarySection
        isLoading={isProjectLoading}
        items={datasets.slice(0, 3)}
        emptyState={<>No datasets yet</>}
        header={
          <>
            <div className="flex w-full items-center gap-2">
              <span className="block flex-none rounded-md p-1.5 bg-fuchsia-50 text-fuchsia-500 dark:bg-fuchsia-950/50">
                <Database className="size-4" />
              </span>
              <span className="flex-1">Recent datasets</span>
              <Button
                size="xs"
                variant="ghost"
                className="text-primary-400"
                onClick={() =>
                  openCreateDatasetModal(`Dataset ${datasets.length + 1}`)
                }
                Icon={Plus}
              >
                Create
              </Button>
              <Link
                href={getDatasetsLink({ orgName, projectName })}
                className={cn(
                  buttonVariants({ size: "xs", variant: "ghost" }),
                  "text-primary-400",
                )}
              >
                View all
              </Link>
              {createDatasetModal}
            </div>
          </>
        }
        renderItem={(dataset) => {
          return (
            <TileListItem
              key={dataset.id}
              title={dataset.name}
              subtitle={
                dataset.created
                  ? smartDateFormat(new Date(dataset.created).getTime())
                  : ""
              }
              className="h-20 w-full p-3 text-xs transition-colors bg-fuchsia-50 hover:bg-fuchsia-100 dark:bg-fuchsia-950/50 dark:hover:bg-fuchsia-900"
              href={getDatasetLink({
                orgName,
                projectName,
                datasetName: dataset.name,
              })}
            />
          );
        }}
      />

      <LibrarySection
        isLoading={promptsLoading}
        items={prompts}
        errorMessage={
          promptsError ? "There was a problem loading prompts" : undefined
        }
        emptyState={<>No prompts yet</>}
        header={
          <>
            <div className="flex w-full items-center gap-2">
              <span className="block flex-none rounded-md p-1.5 bg-cyan-50 text-cyan-500 dark:bg-cyan-950/50">
                <MessageCircle className="size-4" />
              </span>
              Recent prompts
              <div className="flex flex-1 justify-end">
                <Link
                  href={getSavedPromptLink({
                    orgName,
                    projectSlug: projectName,
                    type: "prompt",
                    promptId: "new",
                  })}
                  className={cn(
                    buttonVariants({
                      variant: "ghost",
                      size: "xs",
                      className: "text-primary-400",
                    }),
                  )}
                >
                  <Plus className="size-3 flex-none" />
                  Create
                </Link>
                <Link
                  href={`${getProjectLink({
                    orgName,
                    projectName,
                  })}/prompts`}
                  className={cn(
                    buttonVariants({ size: "xs", variant: "ghost" }),
                    "text-primary-400",
                  )}
                >
                  View all
                </Link>
              </div>
            </div>
          </>
        }
        renderItem={(prompt) => {
          const model = getModelFromPromptData(prompt.prompt_data);
          return (
            <TileListItem
              key={prompt._xact_id}
              title={prompt.name}
              subtitle={
                <ModelOptionLabel
                  className="w-full gap-1 overflow-hidden"
                  labelClassName="truncate"
                  model={model ?? ""}
                  displayName={model ?? "Unknown model"}
                />
              }
              className="h-20 w-full p-3 text-xs transition-colors bg-cyan-50 hover:bg-cyan-100 dark:bg-cyan-950/50 dark:hover:bg-cyan-900"
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/prompts?pr=${prompt.id}`}
            />
          );
        }}
      />

      <LibrarySection
        isLoading={toolsLoading}
        items={tools}
        errorMessage={
          toolsError ? "There was a problem loading tools" : undefined
        }
        emptyState="No tools yet"
        header={
          <div className="flex w-full items-center gap-2">
            <span className="block flex-none rounded-md p-1.5 bg-amber-50 text-amber-500 dark:bg-amber-950/50">
              <Bolt className="size-4" />
            </span>
            <span className="flex-1">Recent tools</span>
            <Link
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/tools`}
              className={cn(
                buttonVariants({ size: "xs", variant: "ghost" }),
                "text-primary-400",
              )}
            >
              View all
            </Link>
          </div>
        }
        renderItem={(tool) => {
          return (
            <TileListItem
              key={tool.id}
              title={tool.name}
              subtitle={tool.description}
              className="h-20 w-full p-3 text-xs transition-colors bg-amber-50 hover:bg-amber-100 dark:bg-amber-950/50 hover:dark:bg-amber-900"
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/tools?pr=${tool.id}`}
            />
          );
        }}
      />

      <LibrarySection
        isLoading={scorersLoading}
        items={scorers}
        errorMessage={
          scorersError ? "There was a problem loading scorers" : undefined
        }
        emptyState={<>No scorers yet</>}
        header={
          <div className="flex w-full items-center gap-2">
            <span className="block flex-none rounded-md p-1.5 bg-lime-50 text-lime-500 dark:bg-lime-950/50">
              <Percent className="size-4" />
            </span>
            <span className="flex-1">Recent scorers</span>
            <Link
              href={getSavedPromptLink({
                orgName,
                projectSlug: projectName,
                type: "scorer",
                promptId: "new",
              })}
              className={cn(
                buttonVariants({
                  variant: "ghost",
                  size: "xs",
                  className: "text-primary-400",
                }),
              )}
            >
              <Plus className="size-3 flex-none" />
              Create
            </Link>
            <Link
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/scorers`}
              className={cn(
                buttonVariants({ size: "xs", variant: "ghost" }),
                "text-primary-400",
              )}
            >
              View all
            </Link>
          </div>
        }
        renderItem={(scorer) => {
          const scorerType = getScorerType(scorer);
          return (
            <TileListItem
              key={scorer.id}
              prefetch
              title={scorer.name}
              subtitle={scorerType}
              className="h-20 w-full p-3 text-xs transition-colors bg-lime-50 hover:bg-lime-100 dark:bg-lime-950/50 hover:dark:bg-lime-900"
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/scorers?pr=${scorer.id}`}
            />
          );
        }}
      />

      <LibrarySection
        isLoading={agentsLoading}
        items={agents}
        errorMessage={
          agentsError ? "There was a problem loading agents" : undefined
        }
        emptyState="No agents yet"
        header={
          <>
            <div className="flex w-full items-center gap-2">
              <span className="block flex-none rounded-md p-1.5 bg-red-50 text-red-500 dark:bg-red-950/50">
                <Route className="size-4" />
              </span>
              <span className="flex-1">Recent agents</span>
              <Link
                href={`${getProjectLink({
                  orgName,
                  projectName,
                })}/agents`}
                className={cn(
                  buttonVariants({ size: "xs", variant: "ghost" }),
                  "text-primary-400",
                )}
              >
                View all
              </Link>
            </div>
          </>
        }
        renderItem={(agent) => {
          return (
            <TileListItem
              key={agent.id}
              title={agent.name}
              subtitle={agent.description}
              className="h-20 w-full p-3 text-xs transition-colors bg-red-50 hover:bg-red-100 dark:bg-red-950/50 hover:dark:bg-red-900"
              href={`${getProjectLink({
                orgName,
                projectName,
              })}/agents?pr=${agent.id}`}
            />
          );
        }}
      />
    </div>
  );
};

const LibrarySection = <TItem extends {}>({
  items,
  isLoading,
  header,
  errorMessage,
  renderItem,
  emptyState = null,
}: {
  items?: TItem[] | null;
  isLoading: boolean;
  header: React.ReactNode;
  errorMessage?: string;
  renderItem: (item: TItem) => React.ReactNode;
  emptyState?: React.ReactNode;
}) => {
  const loading = isLoading || items === null || items === undefined;
  const empty = !loading && items && items.length === 0;

  if (empty && !emptyState) return null;

  return (
    <div className="group flex flex-col">
      <h2 className="mb-5 flex items-center justify-between text-xs text-primary-600">
        {header}
      </h2>
      {loading ? (
        <TileList className="grid grid-cols-3">
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
          <Skeleton className="h-20 w-full" />
        </TileList>
      ) : errorMessage ? (
        <div className="flex flex-col gap-2 rounded-md p-4 text-center text-xs text-primary-500">
          {errorMessage}
        </div>
      ) : empty && emptyState ? (
        <div className="flex h-20 items-center justify-center gap-3 rounded-md border text-xs bg-primary-50 border-primary-200/50 text-primary-400">
          {emptyState}
        </div>
      ) : (
        <TileList className="grid grid-cols-3">
          {items.map(renderItem)}
        </TileList>
      )}
    </div>
  );
};

const getModelFromPromptData = (promptData: string) => {
  try {
    const parsedData = JSON.parse(promptData);
    return parsedData.options.model;
  } catch (e) {
    return "";
  }
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getScorerType = (scorer: any) => {
  let functionData;
  try {
    functionData = JSON.parse(scorer.function_data);
  } catch (e) {
    return "LLM judge";
  }

  if (functionData.type === "code") {
    return functionData.data.runtime_context.runtim === "node"
      ? "TypeScript"
      : "Python";
  }

  return "LLM judge";
};
