import { Button, type ButtonProps } from "#/ui/button";
import { Plus } from "lucide-react";
import {
  forwardRef,
  type HTMLAttributes,
  useCallback,
  useContext,
  useMemo,
} from "react";
import { type UIFunctionData, type UIFunction } from "#/ui/prompts/schema";
import { ProjectContext } from "../projectContext";
import { type DropdownMenuContentProps } from "@radix-ui/react-dropdown-menu";
import { NestedDropdown } from "#/ui/nested-dropdown";
import { DropdownMenuItem } from "#/ui/dropdown-menu";
import { isEmpty } from "#/utils/object";
import { type PromptData } from "@braintrust/core/typespecs";
import { DEFAULT_GRAPH } from "../prompts/agent-canvas-utils";
import { makePromptDropdownContext } from "./prompts-dropdown";

const [AgentsDropdownProvider, useAgentsDropdown] = makePromptDropdownContext({
  name: "Agents",
  args: {
    functionObjectType: "agent",
    query: "", // not used
    btqlFilter: "function_data.type = 'graph'",
  },
});

export { AgentsDropdownProvider, useAgentsDropdown };

type AgentItem = UIFunction & {
  projectName?: string;
  disabled?: boolean;
};

export const AgentsDropdown = ({
  onAddAgent,
  projectName,
  selectedAgentIds = [],
  disabledOriginIds = [],
  buttonProps,
  dropdownMenuContentProps,
  children,
  open,
  isInSubMenu,
  hideBlankOption,
}: React.PropsWithChildren<{
  onAddAgent: (agent: {
    functionData?: UIFunctionData;
    origin?: PromptData["origin"];
  }) => void;
  projectName: string;
  selectedAgentIds: string[];
  disabledOriginIds?: string[];
  buttonProps?: ButtonProps;
  dropdownMenuContentProps?: DropdownMenuContentProps;
  open?: boolean;
  isInSubMenu?: boolean;
  hideBlankOption?: boolean;
}>) => {
  const { projectId } = useContext(ProjectContext);
  const { promptsByProjectSorted: agentsByProjectSorted, isLoading } =
    useAgentsDropdown();

  const dropdownData = useMemo(() => {
    if (isEmpty(agentsByProjectSorted)) {
      return { items: undefined, subGroups: undefined };
    }

    const mainItems =
      agentsByProjectSorted[0]?.projectName === projectName
        ? agentsByProjectSorted[0].prompts.map((p) => ({
            ...p,
            disabled: disabledOriginIds.includes(p.id),
          }))
        : undefined;

    const otherProjects = agentsByProjectSorted
      .filter(
        ({ projectName: sortedProjectName }) =>
          sortedProjectName !== projectName,
      )
      .flatMap(({ projectName, prompts }) =>
        prompts.map((p) => ({
          ...p,
          projectName,
          disabled: disabledOriginIds.includes(p.id),
        })),
      );

    const subGroups =
      otherProjects.length > 0
        ? [{ groupLabel: "Other projects", items: otherProjects }]
        : undefined;

    return {
      items: mainItems
        ? { groupLabel: "This project", items: mainItems }
        : undefined,
      subGroups,
    };
  }, [agentsByProjectSorted, projectName, disabledOriginIds]);

  const selectedPrompts = useMemo(
    () =>
      agentsByProjectSorted
        .flatMap((project) =>
          project.prompts.map((p) => ({
            ...p,
            projectName: project.projectName,
            disabled: disabledOriginIds.includes(p.id),
          })),
        )
        .filter((p) => selectedAgentIds.includes(p.id)),
    [agentsByProjectSorted, selectedAgentIds, disabledOriginIds],
  );

  const PromptMenuItem = forwardRef<
    HTMLDivElement,
    { item: AgentItem } & HTMLAttributes<HTMLDivElement>
  >(
    useCallback(
      ({ item: agent, ...rest }, ref) => {
        return (
          <DropdownMenuItem
            ref={ref}
            {...rest}
            className="flex gap-2"
            disabled={agent.disabled}
            onSelect={() => {
              onAddAgent({
                functionData: agent.function_data,
                origin: {
                  prompt_id: agent.id,
                  project_id: agent.project_id,
                  prompt_version: agent._xact_id,
                },
              });
            }}
            title={`${agent.name} from ${agent.projectName ?? "this project"}`}
          >
            <span className="flex-1">{agent.name}</span>
            {agent.project_id === projectId ? null : (
              <span className="max-w-24 flex-none truncate text-primary-500">
                {agent.projectName}
              </span>
            )}
          </DropdownMenuItem>
        );
      },
      [onAddAgent, projectId],
    ),
  );

  return (
    <NestedDropdown<AgentItem>
      open={open}
      isInSubMenu={isInSubMenu}
      objectType="agent"
      align={dropdownMenuContentProps?.align}
      dropdownItems={dropdownData.items}
      subGroups={dropdownData.subGroups}
      filterItems={(search, opts) =>
        opts.filter((opt) =>
          opt.name?.toLocaleLowerCase().includes(search.toLocaleLowerCase()),
        )
      }
      DropdownItemComponent={PromptMenuItem}
      selectedItems={selectedPrompts}
      additionalActions={
        hideBlankOption
          ? []
          : [
              {
                label: (
                  <div className="flex items-center gap-2">
                    <Plus className="size-3" />
                    Blank agent
                  </div>
                ),
                onSelect: () =>
                  onAddAgent({
                    functionData: DEFAULT_GRAPH,
                  }),
              },
            ]
      }
    >
      {isInSubMenu
        ? (children ?? (
            <Button
              size="xs"
              variant="ghost"
              className="gap-1"
              isLoading={isLoading}
              Icon={Plus}
              {...buttonProps}
            >
              Agent
            </Button>
          ))
        : null}
    </NestedDropdown>
  );
};
