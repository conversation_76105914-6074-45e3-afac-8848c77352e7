import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import { cn } from "#/utils/classnames";
import { MessageCircle, Percent, Plus, Route, Unplug } from "lucide-react";
import { PromptsDropdown } from "../prompts-dropdown";
import { RemoteEvalsDropdown } from "../remote-evals-dropdown";
import { AgentsDropdown } from "../agents-dropdown";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useState, type PropsWithChildren } from "react";
import { usePlaygroundPromptSheetIndexState } from "#/ui/query-parameters";
import {
  type AppendEditorArgs,
  useSyncedPrompts,
} from "../../prompts/synced/use-synced-prompts";
import { ScorersDropdown } from "../scorers-dropdown";

export const PlaygroundAddTask = ({
  isReadOnly,
  numTasks,
  projectName,
  children,
  disabledOriginIds = [],
}: PropsWithChildren<{
  isReadOnly?: boolean;
  numTasks?: number;
  projectName: string;
  disabledOriginIds?: string[];
}>) => {
  const { setPromptSheetIndex } = usePlaygroundPromptSheetIndexState();
  const { appendNewEditor_ROOT } = useSyncedPrompts();

  const onAdd = (item: AppendEditorArgs) => {
    appendNewEditor_ROOT(item);
    setPromptSheetIndex(numTasks ?? 0);
  };

  return (
    <AddTask
      projectName={projectName}
      onAdd={onAdd}
      disabledOriginIds={disabledOriginIds}
    >
      {children ?? (
        <Button
          size="xs"
          Icon={Plus}
          className={cn("flex-none", { hidden: isReadOnly })}
        >
          Task
          {!!numTasks && numTasks > 0 && (
            <span className="text-[10px] font-normal text-primary-500">
              {numTasks}
            </span>
          )}
        </Button>
      )}
    </AddTask>
  );
};

export const AddTask = ({
  projectName,
  children,
  hideRemoteEvals,
  disallowBlankTasks,
  disabledOriginIds,
  align = "end",
  onAdd,
}: PropsWithChildren<{
  projectName: string;
  hideRemoteEvals?: boolean;
  align?: "start" | "end";
  disallowBlankTasks?: boolean;
  disabledOriginIds?: string[];
  onAdd: (task: AppendEditorArgs) => void;
}>) => {
  const [open, setOpen] = useState(false);
  const {
    flags: { agents, remoteEvals, scorerTasks },
  } = useFeatureFlags();

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent align={align}>
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <MessageCircle className="size-3" />
            Prompt
          </DropdownMenuSubTrigger>
          <PromptsDropdown
            onAddPrompt={onAdd}
            isInSubMenu
            open={open}
            hideBlankOption={disallowBlankTasks}
            projectName={projectName}
            disabledOriginIds={disabledOriginIds}
            dropdownMenuContentProps={{
              align: "start",
            }}
          />
        </DropdownMenuSub>
        {agents && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Route className="size-3" />
              Agent
            </DropdownMenuSubTrigger>
            <AgentsDropdown
              isInSubMenu
              open={open}
              projectName={projectName}
              selectedAgentIds={[]}
              disabledOriginIds={disabledOriginIds}
              dropdownMenuContentProps={{
                align: "start",
              }}
              hideBlankOption={disallowBlankTasks}
              onAddAgent={onAdd}
            />
          </DropdownMenuSub>
        )}
        {remoteEvals && !hideRemoteEvals && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Unplug className="size-3" />
              Remote eval
            </DropdownMenuSubTrigger>
            <RemoteEvalsDropdown isInSubMenu open={open} onAddEval={onAdd} />
          </DropdownMenuSub>
        )}
        {scorerTasks && (
          <DropdownMenuSub>
            <DropdownMenuSubTrigger>
              <Percent className="size-3" />
              Scorers
            </DropdownMenuSubTrigger>
            <ScorersDropdown
              onAddScorer={onAdd}
              isInSubMenu
              open={open}
              hideBlankOption={disallowBlankTasks}
              projectName={projectName}
              disabledOriginIds={disabledOriginIds}
              dropdownMenuContentProps={{
                align: "start",
              }}
            />
          </DropdownMenuSub>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
