import { type PromptSessionState } from "#/ui/prompts/schema";
import { useMemo } from "react";
import { type SavedPromptMeta } from "../use-saved-prompt-meta";
import { sortPrompts } from "../playground-controls";
import { AvailableModels } from "@braintrust/proxy/schema";
import { useJSONMemo } from "#/utils/memo";

export const usePromptDisplayNames = ({
  promptSessionState,
  savedPromptMeta,
}: {
  promptSessionState?: PromptSessionState | null;
  savedPromptMeta: Record<string, SavedPromptMeta | undefined>;
}) => {
  const sortedPrompts = useMemo(
    () => sortPrompts(promptSessionState),
    [promptSessionState],
  );

  const displayNames = useJSONMemo(
    useMemo(() => {
      const promptDisplays: { name: string; id: string }[] = [];
      const promptDisplayNamesById: Record<string, string> = {};
      sortedPrompts.forEach((p, index) => {
        const name = (() => {
          const promptId = p.prompt_data.origin?.prompt_id;
          const isAgent = p.function_data.type === "graph";
          const isScorer = p.function_type === "scorer";
          const isRemoteEval = p.function_data.type === "remote_eval";
          const meta = promptId ? savedPromptMeta[promptId] : undefined;
          if (meta?.name) {
            return meta.name;
          }

          if (isAgent) {
            return "Agent";
          }

          if (isRemoteEval) {
            return p.function_data.type === "remote_eval"
              ? p.function_data.eval_name
              : "Remote eval";
          }

          const model = p.prompt_data.options?.model;
          const modelDisplayName = model
            ? AvailableModels[model]?.displayName
            : null;
          if (modelDisplayName) {
            return modelDisplayName;
          }

          if (model) {
            return model;
          }

          return `${isScorer ? "Scorer" : "Prompt"} ${String.fromCharCode(
            "A".charCodeAt(0) + index,
          )}`;
        })();
        promptDisplays.push({ name, id: p.id });
        promptDisplayNamesById[p.id] = name;
      });

      return { promptDisplays, promptDisplayNamesById };
    }, [sortedPrompts, savedPromptMeta]),
  );

  return displayNames;
};
