"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import "#/styles/globals.css";
import { useContext, useRef } from "react";
import { FunctionsListWithSearch } from "#/ui/prompts/functions-list-with-search";
import { Skeleton } from "#/ui/skeleton";
import { isEmpty } from "#/utils/object";
import { AccessFailed } from "#/ui/access-failed";
import { ProjectListLayout } from "../project-list-layout";

export interface Params {
  org: string;
  project: string;
}

export default function ClientPage({ params }: { params: Params }) {
  const projectName = decodeURIComponent(params.project);
  const orgName = decodeURIComponent(params.org);

  const { projectId } = useContext(ProjectContext);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return (
    <ProjectListLayout
      active="tools"
      orgName={orgName}
      projectName={projectName}
      scrollContainerRef={scrollContainerRef}
    >
      {projectId ? (
        <FunctionsListWithSearch
          functionObjectType="tool"
          viewType="tools"
          scrollContainerRef={scrollContainerRef}
          placeholder="Find tools"
        />
      ) : (
        <Skeleton />
      )}
    </ProjectListLayout>
  );
}
