import { <PERSON><PERSON>, buttonVariants } from "#/ui/button";
import { BasicTooltip } from "#/ui/tooltip";
import { cn } from "#/utils/classnames";

import "@xyflow/react/dist/style.css";
import React, { memo, type RefObject, useMemo, useRef, useState } from "react";
import {
  type PromptData,
  type UIFunctionData,
  createOrUpdatePrompt,
  newPrompt,
  promptSchema,
} from "#/ui/prompts/schema";
import { Popover, PopoverContent, PopoverTrigger } from "#/ui/popover";
import { Skeleton } from "#/ui/skeleton";
import { isEmpty } from "#/utils/object";
import { type SavingState } from "#/ui/saving";
import { Spinner } from "#/ui/icons/spinner";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import Link from "next/link";
import { useOpenedPlaygroundPrompt } from "../open";
import { TransactionIdField } from "@braintrust/local/query";
import { prettifyXact } from "@braintrust/core";
import { UpdatePromptDialog } from "../update-prompt-dialog";
import { toast } from "sonner";
import { type ZodError } from "zod";
import { zodErrorToString } from "#/utils/validation";
import { useOrg } from "#/utils/user";
import {
  FunctionDescriptionField,
  FunctionMetadataField,
  FunctionNameField,
} from "#/ui/prompts/function-editor/function-meta-fields";
import { deepEqual, excludeKeys } from "../utils";
import { useHotkeys } from "react-hotkeys-hook";
import useEvent from "react-use-event-hook";
import { useProjectPromptsDropdown } from "../../playgrounds/prompts-dropdown";
import { useSyncedPrompts } from "./use-synced-prompts";
import { useQueryFunc } from "#/utils/react-query";
import { type getProjectSummary } from "#/app/app/[org]/org-actions";
import {
  PromptVersionItem,
  VersionSelector,
} from "#/app/app/[org]/prompt/[prompt]/version-selector";
import { useUpsellContext } from "#/app/playground/upsell-dialog";
import { useAgentsDropdown } from "../../playgrounds/agents-dropdown";
import { useScorersDropdown } from "../../playgrounds/scorers-dropdown";
import { MessageCircle, Percent, Route } from "lucide-react";

type Props = {
  containerRef: RefObject<HTMLDivElement | null>;
  functionData?: UIFunctionData;
  isReadOnly?: boolean;
  orgName: string;
  promptData?: PromptData;
  origin?: PromptData["origin"];
  promptId: string;
  projectId?: string | null;
  savedPromptMetaName?: string | null;
  type: "prompt" | "agent" | "scorer";
};

export const SavedTaskBarSynced = (props: Props) => {
  const { onUpsell } = useUpsellContext();

  return (
    <div className="flex flex-none gap-2 truncate border-t p-2">
      {onUpsell ? (
        <Button variant="ghost" size="xs" onClick={onUpsell}>
          {`Save ${props.type}`}
        </Button>
      ) : (
        <SavedTaskBarSyncedInner {...props} />
      )}
    </div>
  );
};

const SavedTaskBarSyncedInner = memo(
  ({
    containerRef,
    functionData = { type: "prompt" },
    isReadOnly,
    orgName,
    promptData = {},
    origin,
    promptId,
    projectId,
    savedPromptMetaName,
    type,
  }: Props) => {
    const originPromptId = origin?.prompt_id;
    const originProjectId = origin?.project_id;
    const originPromptVersion = origin?.prompt_version;

    const [savingState, setSavingState] = useState<SavingState>("none");
    const openedPrompt = useOpenedPlaygroundPrompt({
      promptId: originPromptId,
      projectId: originProjectId,
      promptVersion: originPromptVersion,
      setSavingState,
    });

    const openedPromptLatest = useOpenedPlaygroundPrompt({
      promptId: originPromptId,
      projectId: originProjectId,
      promptVersion: undefined, // This will pull the latest.
      setSavingState,
    });

    const { invalidate: invalidatePrompts } = useProjectPromptsDropdown();
    const { invalidate: invalidateAgents } = useAgentsDropdown();
    const { invalidate: invalidateScorer } = useScorersDropdown();

    const { updateOrigin, resetPrompt, resetFunction_ROOT } =
      useSyncedPrompts();

    const isSavedPrompt =
      !!originPromptId &&
      openedPrompt.status !== "not_found" &&
      // If the latest prompt is not found, it means that the prompt has been deleted.
      openedPromptLatest.status !== "not_found";

    const [saveMenuOpened, setSaveMenuOpened] = useState(false);
    const [inputName, setInputName] = useState<string | null>(null);
    const [inputSlug, setInputSlug] = useState<string | null>(null);
    const [inputDescription, setInputDescription] = useState<string | null>(
      null,
    );
    const [inputMetadata, setInputMetadata] = useState<Record<
      string,
      unknown
    > | null>(null);
    const [error, setError] = useState<string | null>(null);

    const savedName = openedPrompt.prompt?.name ?? savedPromptMetaName ?? "";
    const name = inputName ?? (isSavedPrompt ? savedName : "");
    const slug = inputSlug ?? (isSavedPrompt ? openedPrompt.prompt?.slug : "");
    const description =
      inputDescription ??
      (isSavedPrompt ? openedPrompt.prompt?.description : "");
    const metadata =
      inputMetadata ??
      (isSavedPrompt ? openedPrompt.prompt?.metadata : undefined);
    const org = useOrg();
    const { data: projects, isLoading: isLoadingProjects } = useQueryFunc<
      typeof getProjectSummary
    >({
      fName: "getProjectSummary",
      args: {
        org_name: orgName,
      },
    });
    const originProjectName = projects?.find(
      (p) => p.project_id === originProjectId,
    )?.project_name;

    const savePrompt = useEvent(async () => {
      if (!(openedPrompt.prompt || projectId)) {
        throw new Error("No project specified");
      }
      if (!org.id) {
        throw new Error("Org id is missing");
      }

      const prompt = {
        ...(isSavedPrompt && !!openedPrompt.prompt
          ? openedPrompt.prompt
          : newPrompt(projectId!)),
        name,
        slug,
        description,
        metadata,
        prompt_data: promptData,
        function_data: functionData,
      };

      const xactId = await createOrUpdatePrompt({
        dml: openedPrompt.dml,
        orgId: org.id,
        update: isSavedPrompt,
        // If we change the code to show FunctionSlugField, we should set this if the slug is changed.
        updateSlug: false,
        prompt,
        onOptimisticUpdate: () => {
          setSaveMenuOpened(false);
        },
      });

      updateOrigin({
        id: promptId,
        origin: {
          project_id: prompt.project_id,
          prompt_id: prompt.id,
          prompt_version: xactId ?? undefined,
        },
      });

      if (type === "prompt") {
        invalidatePrompts();
      } else if (type === "agent") {
        invalidateAgents();
      } else if (type === "scorer") {
        invalidateScorer();
      }

      return xactId;
    });

    const updatePromptButtonRef = useRef<HTMLButtonElement>(null);
    const onSave = async () => {
      if (isSavedPrompt) {
        updatePromptButtonRef.current?.click();
        return;
      }

      try {
        await savePrompt();
      } catch (e) {
        toast.error(`Error saving ${type}`, {
          description: `${e}`,
        });
      }
    };
    useHotkeys(
      "Mod+S",
      () => {
        if (isReadOnly) return;
        if (containerRef.current?.contains(document.activeElement)) {
          onSave();
        }
      },
      {
        enableOnContentEditable: true, // codemirror is contenteditable
        preventDefault: true,
      },
    );

    const onResetPrompt = (
      savedPrompt: ReturnType<typeof useOpenedPlaygroundPrompt>,
    ) => {
      if (
        savedPrompt.status !== "loaded" ||
        (!savedPrompt.prompt?.prompt_data && !savedPrompt.prompt?.function_data)
      ) {
        toast.error(`Could not reset ${type} (still initializing)`);
        return;
      }
      return type === "prompt"
        ? resetPrompt({ id: promptId, prompt: savedPrompt.prompt })
        : resetFunction_ROOT({ id: promptId, prompt: savedPrompt.prompt });
    };

    const version = openedPrompt.prompt?.[TransactionIdField];
    const latestVersion =
      openedPromptLatest.status === "loaded"
        ? openedPromptLatest.prompt?.[TransactionIdField]
        : undefined;

    const { isDirty, isBehind } = useMemo(() => {
      if (!isSavedPrompt) return { isDirty: false, isBehind: false };

      // deeply compare current state with the saved prompt state
      const isDirty = !deepEqual(
        {
          name,
          description: description ?? "",
          metadata: metadata ?? {},
          options: excludeKeys(promptData?.options, ["position"]) ?? {},
          parser: promptData?.parser,
          prompt: promptData?.prompt,
          function_data: functionData,
        },
        {
          name: openedPrompt.prompt?.name,
          description: openedPrompt.prompt?.description ?? "",
          metadata: openedPrompt.prompt?.metadata ?? {},
          options:
            excludeKeys(openedPrompt.prompt?.prompt_data?.options, [
              "position",
            ]) ?? {},
          parser: openedPrompt.prompt?.prompt_data?.parser,
          prompt: openedPrompt.prompt?.prompt_data?.prompt,
          function_data: openedPrompt.prompt?.function_data,
        },
      );
      const isBehind = latestVersion !== undefined && version !== latestVersion;

      return { isDirty, isBehind };
    }, [
      isSavedPrompt,
      name,
      description,
      metadata,
      promptData?.options,
      promptData?.parser,
      promptData?.prompt,
      functionData,
      openedPrompt.prompt?.name,
      openedPrompt.prompt?.description,
      openedPrompt.prompt?.metadata,
      openedPrompt.prompt?.prompt_data?.options,
      openedPrompt.prompt?.prompt_data?.parser,
      openedPrompt.prompt?.prompt_data?.prompt,
      openedPrompt.prompt?.function_data,
      latestVersion,
      version,
    ]);

    const dirtyPromptMessage =
      isDirty && isBehind
        ? `This ${type} has diverged from the current version of the ${type}${
            version ? ` (${prettifyXact(version)})` : ""
          }`
        : isDirty
          ? `This ${type} has unsaved changes`
          : `This ${type}${
              version ? ` (${prettifyXact(version)})` : ""
            } is behind the latest version${
              latestVersion ? ` (${prettifyXact(latestVersion)})` : ""
            } of the ${type}`;

    const isSaveDisabled =
      savingState === "saving" || !name || !slug || !!error;
    let icon = null;
    switch (type) {
      case "prompt":
        icon = <MessageCircle className="size-3" />;
        break;
      case "agent":
        icon = <Route className="size-3" />;
        break;
      case "scorer":
        icon = <Percent className="size-3" />;
        break;
    }

    return (
      <>
        {openedPrompt.status === "loading" || isLoadingProjects ? (
          <Skeleton className="h-7 flex-1" />
        ) : (
          <>
            <div className="flex flex-1 overflow-hidden">
              <Popover open={saveMenuOpened} onOpenChange={setSaveMenuOpened}>
                <PopoverTrigger asChild>
                  <Button
                    variant={isSavedPrompt ? undefined : "ghost"}
                    className={cn(
                      "gap-1 flex items-center max-w-full truncate",
                      { "rounded-r-none border-r-0": isSavedPrompt },
                    )}
                    size="xs"
                  >
                    {icon}
                    {isSavedPrompt ? (
                      <span className="max-w-full truncate">{savedName}</span>
                    ) : (
                      `Save ${type}`
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  align="start"
                  className="flex flex-col gap-3 overflow-y-auto"
                >
                  {!isSavedPrompt && (
                    <div className="w-full">
                      <div className="font-semibold">Save this prompt</div>
                      <div className="mb-4 text-pretty text-xs text-primary-500">
                        Library {type}s can be used in any&nbsp;playground
                      </div>
                    </div>
                  )}
                  {error && (
                    <div className="mb-2 text-xs text-bad-500">{error}</div>
                  )}

                  <FunctionNameField
                    name={name}
                    isUpdate={isSavedPrompt}
                    onChange={({ name: newName, slug: newSlug }) => {
                      setInputName(newName);
                      if (newSlug) {
                        setInputSlug(newSlug);
                      }
                    }}
                  />

                  <FunctionDescriptionField
                    description={description ?? ""}
                    onChange={setInputDescription}
                  />

                  <FunctionMetadataField
                    metadata={metadata}
                    onChange={async (v) => {
                      // TODO: debounce parsing?
                      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
                      setInputMetadata(v as Record<string, unknown> | null);
                      try {
                        promptSchema.shape.metadata.parse(v);
                        setError(null);
                      } catch (error) {
                        setError(
                          "Invalid metadata: " +
                            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
                            zodErrorToString(error as ZodError, 0, false),
                        );
                      }
                      // returning "0" as fallback to effectively disable optimistic updates in UpdateableDataTextEditor
                      return openedPrompt.prompt?._xact_id ?? "0";
                    }}
                    functionId={openedPrompt?.prompt?.id ?? ""}
                  />
                  <div className="flex gap-2">
                    <BasicTooltip
                      tooltipContent={
                        isSaveDisabled
                          ? error
                            ? error
                            : savingState === "saving"
                              ? "Saving prompt..."
                              : !name
                                ? "Please enter a name for the prompt"
                                : !slug
                                  ? "Please enter a slug for the prompt"
                                  : null
                          : null
                      }
                    >
                      <Button
                        size="xs"
                        variant="primary"
                        onClick={async (e) => {
                          e.stopPropagation();
                          await onSave();
                        }}
                        disabled={isSaveDisabled}
                        className="disabled:pointer-events-auto"
                      >
                        {savingState === "saving" ? (
                          <>
                            <Spinner className="mr-2" />
                            Saving
                          </>
                        ) : isSavedPrompt ? (
                          "Update"
                        ) : (
                          "Save"
                        )}
                      </Button>
                    </BasicTooltip>
                    {isSavedPrompt &&
                      openedPrompt.prompt &&
                      originProjectName &&
                      (type === "prompt" || type === "scorer") && (
                        <Link
                          href={getSavedPromptLink({
                            orgName,
                            projectSlug: originProjectName,
                            promptId: openedPrompt.prompt?.id,
                            type,
                          })}
                          className={buttonVariants({
                            variant: "ghost",
                            size: "xs",
                            className: "border",
                          })}
                        >
                          Go to {type}
                        </Link>
                      )}
                  </div>
                </PopoverContent>
              </Popover>
              {isSavedPrompt && !isEmpty(version) && (
                <VersionSelector
                  rowId={originPromptId}
                  objectId={originProjectId ?? null}
                  objectType={
                    type === "prompt" ? "project_prompts" : "project_functions"
                  }
                  selectedVersion={version}
                  onVersionChange={onResetPrompt}
                  VersionItemRenderer={PromptVersionItem}
                />
              )}
            </div>
            {isSavedPrompt && (isDirty || isBehind) && (
              <UpdatePromptDialog
                type={type}
                functionData={functionData}
                dirtyPromptMessage={dirtyPromptMessage}
                name={name}
                description={description}
                metadata={metadata}
                isDirty={isDirty}
                isBehind={isBehind}
                openedPrompt={openedPrompt.prompt}
                latestPrompt={openedPromptLatest.prompt}
                promptData={promptData}
                buttonRef={updatePromptButtonRef}
                onSave={async () => {
                  try {
                    await savePrompt();
                  } catch (e) {
                    toast.error(`Error saving ${type}`, {
                      description: `${e}`,
                    });
                  }
                }}
                onResetToOpened={async () => {
                  try {
                    await onResetPrompt(openedPrompt);
                  } catch (e) {
                    toast.error(`Error resetting ${type}`, {
                      description: `${e}`,
                    });
                  }
                }}
                onResetToLatest={async () => {
                  try {
                    await onResetPrompt(openedPromptLatest);
                  } catch (e) {
                    toast.error(`Error resetting ${type}`, {
                      description: `${e}`,
                    });
                  }
                }}
              />
            )}
          </>
        )}
      </>
    );
  },
);
SavedTaskBarSyncedInner.displayName = "SavedTaskBarSyncedInner";
