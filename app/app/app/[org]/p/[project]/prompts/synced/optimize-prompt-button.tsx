import { But<PERSON> } from "#/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "#/ui/dropdown-menu";
import {
  type ContextObject,
  type UserMessage,
} from "#/ui/optimization/use-global-chat-context";
import { cn } from "#/utils/classnames";
import { Blend } from "lucide-react";
import { BasicTooltip } from "#/ui/tooltip";
import { type SavedPromptMeta } from "../../playgrounds/[playground]/use-saved-prompt-meta";
import { type PromptData, type UIFunctionData } from "#/ui/prompts/schema";
const OptimizePromptButton = ({
  promptId,
  promptMeta,
  promptData,
  functionData,
  currentModel,
  index,
  handleSendMessage,
  setIsChatOpen,
  setCurrentSessionContextObjects,
  containerRef,
}: {
  promptId: string;
  currentModel: string;
  index: number;
  promptMeta?: SavedPromptMeta;
  promptData?: PromptData;
  functionData?: UIFunctionData;
  handleSendMessage: (
    userMessage: UserMessage,
    options?: {
      clearContextObjects?: boolean;
      clearUserMessage?: boolean;
    },
  ) => Promise<void>;
  setIsChatOpen: (isChatOpen: boolean) => void;
  setCurrentSessionContextObjects: (
    contextObjects:
      | Record<string, ContextObject>
      | ((
          prev: Record<string, ContextObject>,
        ) => Record<string, ContextObject>),
  ) => void;
  containerRef: React.RefObject<HTMLDivElement | null>;
}) => {
  const container = containerRef.current;
  return (
    <DropdownMenu>
      <BasicTooltip tooltipContent="Optimize task">
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "p-0 size-7 text-primary-500 opacity-0 transition-opacity group-hover/promptblock:opacity-100 group-focus-visible/promptblock:opacity-100",
              "data-[state=open]:opacity-100 data-[state=open]:bg-primary-200 data-[state=open]:text-primary-900",
            )}
            Icon={Blend}
          />
        </DropdownMenuTrigger>
      </BasicTooltip>
      <DropdownMenuContent portalContainer={container} align="end">
        <DropdownMenuItem
          onSelect={(e) => {
            const newItem: ContextObject = {
              id: promptId,
              name: promptMeta?.name ?? `${currentModel} ${index}`,
              description: promptMeta?.description ?? "",
              index: index,
              promptData: promptData ?? {},
              functionData: functionData ?? {},
              metadata: promptMeta?.metadata ?? {},
              resource: "task",
            };
            setCurrentSessionContextObjects((prev) => ({
              ...prev,
              [promptId]: newItem,
            }));
            setTimeout(() => {
              setIsChatOpen(true);
            }, 250);
          }}
        >
          Add to Loop chat
        </DropdownMenuItem>
        <DropdownMenuItem
          onSelect={(e) => {
            const newItem: ContextObject = {
              id: promptId,
              name: promptMeta?.name ?? `${currentModel} ${index}`,
              description: promptMeta?.description ?? "",
              index: index,
              promptData: promptData ?? {},
              functionData: functionData ?? {},
              metadata: promptMeta?.metadata ?? {},
              resource: "task",
            };
            handleSendMessage(
              {
                id: crypto.randomUUID(),
                type: "user_message",
                message: `Optimize the prompt to improve the output of the following task: ${JSON.stringify(newItem, null, 2)}`,
              },
              {
                clearContextObjects: false,
                clearUserMessage: false,
              },
            );
            setTimeout(() => {
              setIsChatOpen(true);
            }, 250);
          }}
        >
          Quick optimize
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default OptimizePromptButton;
