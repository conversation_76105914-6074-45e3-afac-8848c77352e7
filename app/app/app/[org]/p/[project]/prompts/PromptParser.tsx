import { But<PERSON>, buttonVariants } from "#/ui/button";
import { Input } from "#/ui/input";
import { type PromptData } from "#/ui/prompts/schema";
import { Switch } from "#/ui/switch";
import { cn } from "#/utils/classnames";
import { type TransactionId } from "#/utils/duckdb";
import { Trash2 } from "lucide-react";
import { memo, useEffect } from "react";
import { useFieldArray, useForm, Controller } from "react-hook-form";
import type { Control, FieldErrors } from "react-hook-form";

interface ChoiceScore {
  label: string;
  value: number;
}

interface FormData {
  use_cot: boolean;
  choice_scores: ChoiceScore[];
}

const validateValue = (value: number) => {
  if (isNaN(value)) return "Value must be a number";
  if (value < 0 || value > 1) return "Value must be between 0 and 1";
  return true;
};

const coerceChoiceScore = (score: number | undefined) => {
  if (score == null || isNaN(score)) return 0;
  return Math.max(Math.min(score, 1), 0);
};

export const PromptParser = memo(
  ({
    parser,
    saveParser,
    isReadOnly,
  }: {
    parser: NonNullable<PromptData["parser"]>;
    saveParser: (parser: PromptData["parser"]) => Promise<TransactionId | null>;
    isReadOnly?: boolean;
  }) => {
    const {
      control,
      watch,
      setValue,
      formState: { errors },
    } = useForm<FormData>({
      defaultValues: {
        use_cot: parser?.use_cot || false,
        choice_scores: Object.entries(parser.choice_scores).map(
          ([label, value]) => ({
            label,
            value,
          }),
        ),
      },
      mode: "onChange",
    });

    const { fields, append, remove } = useFieldArray({
      control,
      name: "choice_scores",
    });

    const watchedChoiceScores = watch("choice_scores");
    const useCot = watch("use_cot");

    const validateUniqueLabel = (label: string, index: number) => {
      if (!label) return "Label is required";
      const isDuplicate = watchedChoiceScores.some(
        (choice, i) => i !== index && choice?.label.trim() === label.trim(),
      );
      return isDuplicate ? "Label must be unique" : true;
    };

    useEffect(() => {
      const subscription = watch((value, { name }) => {
        if (name && !isReadOnly) {
          const choiceScoresObj = Object.fromEntries(
            value.choice_scores?.map((entry) => [
              (entry?.label ?? "").trim(),
              coerceChoiceScore(entry?.value),
            ]) || [],
          );

          saveParser({
            ...parser,
            use_cot: value.use_cot ?? false,
            choice_scores: choiceScoresObj,
          });
        }
      });

      return () => {
        subscription.unsubscribe();
      };
    }, [watch, saveParser, parser, isReadOnly]);

    const handleUseCotChange = () => {
      setValue("use_cot", !useCot);
    };

    const handleAddChoice = () => {
      append({ label: "", value: 0 });
    };

    const handleRemoveChoice = (index: number) => {
      remove(index);
    };

    return (
      <>
        {!isReadOnly && (
          <div className="mb-6 mt-4">
            <a
              className={cn(
                buttonVariants({
                  size: "xs",
                  variant: "border",
                }),
                "inline-flex gap-1.5 cursor-pointer",
              )}
              onClick={handleUseCotChange}
            >
              <Switch className="scale-90" checked={useCot} />
              Use chain of thought (CoT)
            </a>
          </div>
        )}
        <div className="mb-4 flex flex-col gap-1.5">
          <div className="text-xs font-medium">Choice scores</div>
          <div className="mb-1 max-w-sm text-xs text-primary-500">
            Choice scores are required when using LLM judge scorers. The model
            will be forced to choose one of the choices using a tool schema. All
            choices and scores must be unique.
          </div>
          {fields.length === 0 ? (
            <div className="mb-1 max-w-sm text-xs text-bad-700">
              No choice scores defined
            </div>
          ) : (
            <div className="flex max-w-sm gap-1 pr-8 text-xs text-primary-500">
              <div className="flex-1">Choice</div>
              <div className="w-24 text-right">Score (0 to 1)</div>
            </div>
          )}

          {fields.map((field, index) => (
            <ChoiceEntry
              key={field.id}
              index={index}
              isReadOnly={isReadOnly}
              control={control}
              errors={errors}
              validateUniqueLabel={validateUniqueLabel}
              validateValue={validateValue}
              onRemove={() => handleRemoveChoice(index)}
              isLast={index === 0 && fields.length === 1}
            />
          ))}
          {!isReadOnly && (
            <div>
              <Button
                size="xs"
                disabled={watchedChoiceScores.some(
                  (choice) => choice?.label === "",
                )}
                onClick={handleAddChoice}
              >
                Add choice score
              </Button>
            </div>
          )}
        </div>
      </>
    );
  },
);
PromptParser.displayName = "PromptParser";

type ChoiceEntryProps = {
  isReadOnly?: boolean;
  index: number;
  control: Control<FormData>;
  errors: FieldErrors<FormData>;
  validateUniqueLabel: (label: string, index: number) => true | string;
  validateValue: (value: number) => true | string;
  onRemove: () => void;
  isLast: boolean;
};

function ChoiceEntry({
  isReadOnly,
  index,
  control,
  errors,
  validateUniqueLabel,
  validateValue,
  onRemove,
  isLast,
}: ChoiceEntryProps) {
  const labelError = errors?.choice_scores?.[index]?.label?.message;
  const valueError = errors?.choice_scores?.[index]?.value?.message;

  return (
    <div className="flex max-w-sm gap-1 text-xs">
      <Controller
        control={control}
        name={`choice_scores.${index}.label`}
        rules={{
          required: "Label is required",
          validate: (value) => validateUniqueLabel(value, index),
        }}
        render={({ field }) => (
          <Input
            {...field}
            className={cn("h-7 flex-1 text-xs", {
              "border-bad-700 bg-bad-50 focus-visible:border-bad-700 focus-visible:ring-bad-700":
                labelError,
            })}
            required
            disabled={isReadOnly}
            placeholder="Enter choice label"
          />
        )}
      />
      <Controller
        control={control}
        name={`choice_scores.${index}.value`}
        rules={{
          required: "Value is required",
          validate: validateValue,
        }}
        render={({ field }) => (
          <Input
            {...field}
            className={cn(
              "h-7 w-24 flex-none tabular-nums text-right text-xs",
              {
                "border-bad-700 bg-bad-50 focus-visible:border-bad-700 focus-visible:ring-bad-700":
                  valueError,
              },
            )}
            disabled={isReadOnly}
            min={0}
            required
            type="number"
            step={0.1}
            max={1}
            placeholder="0"
            onChange={(e) => field.onChange(parseFloat(e.target.value))}
          />
        )}
      />
      {!isReadOnly && (
        <Button
          Icon={Trash2}
          size="xs"
          className="flex-none"
          onClick={onRemove}
          disabled={isLast}
        />
      )}
    </div>
  );
}
