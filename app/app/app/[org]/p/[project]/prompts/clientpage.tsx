"use client";

import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import { useContext, useRef } from "react";
import { FunctionsListWithSearch } from "#/ui/prompts/functions-list-with-search";
import { Skeleton } from "#/ui/skeleton";
import { isEmpty } from "#/utils/object";
import { AccessFailed } from "#/ui/access-failed";
import { ProjectListLayout } from "../project-list-layout";
import { decodeURIComponentPatched } from "#/utils/url";

export interface Params {
  org: string;
  project: string;
}

export default function ClientPage({ params }: { params: Params }) {
  const projectName = decodeURIComponentPatched(params.project);
  const orgName = decodeURIComponentPatched(params.org);

  const { projectId } = useContext(ProjectContext);

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  if (isEmpty(projectId)) {
    return <AccessFailed objectType="Project" objectName={projectName} />;
  }

  return (
    <ProjectListLayout
      orgName={orgName}
      projectName={projectName}
      active="prompts"
      scrollContainerRef={scrollContainerRef}
    >
      {projectId ? (
        <FunctionsListWithSearch
          functionObjectType="prompt"
          scrollContainerRef={scrollContainerRef}
          viewType="prompts"
          placeholder="Find prompts"
        />
      ) : (
        <Skeleton />
      )}
    </ProjectListLayout>
  );
}
